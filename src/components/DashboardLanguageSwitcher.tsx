'use client'

import { useLanguage } from '@/context/LanguageContext'
import { useLoading } from '@/context/LoadingContext'
import { useThemeConfig } from '@/context/ThemeContext'
import { useEffect, useState } from 'react'

export default function DashboardLanguageSwitcher() {
  const { language, setLanguage, t } = useLanguage()
  const { showLoading, hideLoading } = useLoading()
  const themeConfig = useThemeConfig()
  const [isChangingLanguage, setIsChangingLanguage] = useState(false)

  // Handle the actual page reload
  useEffect(() => {
    if (isChangingLanguage) {
      // Use a longer delay to ensure the loading animation is visible
      // This gives a better user experience by showing the loading state
      const reloadTimeout = setTimeout(() => {
        try {
          // Force a hard reload to ensure the page is completely refreshed
          window.location.href = window.location.href.split('#')[0]
        } catch (error) {
          console.error('Error during language reload:', error)
          // Fallback to standard reload if the above fails
          window.location.reload()
        }
      }, 500) // Longer timeout to ensure loading animation is visible

      return () => clearTimeout(reloadTimeout)
    }
  }, [isChangingLanguage])

  // Toggle between languages
  const toggleLanguage = () => {
    try {
      // First show loading overlay immediately
      showLoading(t('changing_language'))

      // Get the new language value
      const newLang = language === 'en' ? 'km' : 'en'

      // Set a short timeout to allow the loading overlay to appear
      setTimeout(() => {
        // Then set the language (which updates localStorage/cookies)
        setLanguage(newLang)

        // Log for debugging
        console.log('Language changed to:', newLang)

        // Set flag to trigger the reload effect
        setIsChangingLanguage(true)
      }, 100) // Short delay to ensure loading overlay appears first
    } catch (error) {
      console.error('Error changing language:', error)
      hideLoading()
      // Force reload as a fallback
      window.location.reload()
    }
  }

  return (
    <button
      onClick={toggleLanguage}
      className={`w-8 h-8 sm:w-9 sm:h-9 ${themeConfig.interactiveDark} border ${themeConfig.border} rounded-lg flex items-center justify-center ${themeConfig.textSecondary} ${themeConfig.interactiveHover} ${themeConfig.borderHover} transition-all duration-300 group`}
      title={language === 'en' ? 'ខ្មែរ' : 'English'}
    >
      {language === 'en' ? (
        <span className="text-xs font-semibold">ខ្មែរ</span>
      ) : (
        <span className="text-xs font-semibold">EN</span>
      )}
    </button>
  )
}