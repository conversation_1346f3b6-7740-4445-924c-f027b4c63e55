# Authentication System

This document outlines the authentication system used in the chhlat-bot project, including server-side verification, client-side context, and security patterns.

## Overview

The authentication system provides:
- Supabase-based authentication
- Server-side auth verification for API routes
- Client-side auth context for components
- JWT token generation for webhook authentication
- Session management and security

## Server-Side Authentication

### Auth Verification Utility
```typescript
// /src/utils/auth.ts
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export interface AuthResult {
  authenticated: boolean
  authId: string | null
  clientId: string | null
}

export async function verifyAuth(): Promise<AuthResult> {
  try {
    const cookieStore = cookies()
    
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    const { data: { session }, error } = await supabase.auth.getSession()

    if (error || !session) {
      return { authenticated: false, authId: null, clientId: null }
    }

    // Extract clientId from app_metadata
    const clientId = session.user.app_metadata?.client_id || null

    return { 
      authenticated: true, 
      authId: session.user.id,
      clientId: clientId
    }
  } catch (error) {
    console.error('Auth verification error:', error)
    return { authenticated: false, authId: null, clientId: null }
  }
}
```

### Usage in API Routes
```typescript
// Standard pattern for API authentication with client identification
export async function POST(request: Request) {
  try {
    // 1. Verify authentication and get client ID
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // 2. Validate client ID if required
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }

    // 3. Proceed with authenticated logic using clientId directly
    // No need for separate database query to get client_id
    const result = await performOperation(clientId, authId)
    
  } catch (error) {
    return NextResponse.json({
      success: false,
      body: null,
      error_msg: 'Authentication failed'
    }, { status: 401 })
  }
}
```

## Client-Side Authentication

### Auth Context Provider
```typescript
// /src/context/AuthContext.tsx
'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Session, User } from '@supabase/supabase-js'

interface AuthContextType {
  user: User | null
  session: Session | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signOut: () => Promise<void>
  signUp: (email: string, password: string) => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  
  const supabase = createClientComponentClient()

  useEffect(() => {
    const getSession = async () => {
      const { data: { session } } = await supabase.auth.getSession()
      setSession(session)
      setUser(session?.user ?? null)
      setLoading(false)
    }

    getSession()

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session)
        setUser(session?.user ?? null)
        setLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [supabase])

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    if (error) throw error
  }

  const signOut = async () => {
    const { error } = await supabase.auth.signOut()
    if (error) throw error
  }

  const signUp = async (email: string, password: string) => {
    const { error } = await supabase.auth.signUp({
      email,
      password,
    })
    if (error) throw error
  }

  return (
    <AuthContext.Provider value={{
      user,
      session,
      loading,
      signIn,
      signOut,
      signUp
    }}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
```

### Usage in Components
```typescript
// Using auth context in components
import { useAuth } from '@/context/AuthContext'

export default function DashboardPage() {
  const { user, session, loading, signOut } = useAuth()

  if (loading) {
    return <div>Loading...</div>
  }

  if (!user) {
    return <div>Please sign in</div>
  }

  return (
    <div>
      <h1>Welcome, {user.email}</h1>
      <button onClick={signOut}>Sign Out</button>
    </div>
  )
}
```

## JWT Token Generation

### Webhook Authentication
```typescript
// /src/utils/jwt.ts
import jwt from 'jsonwebtoken'

export function generateWebhookToken(): string {
  const secret = process.env.CHHLAT_DB_WEBHOOK_TOKEN
  if (!secret) {
    throw new Error('CHHLAT_DB_WEBHOOK_TOKEN environment variable is required')
  }

  return jwt.sign(
    {
      iat: Math.floor(Date.now() / 1000), // issued at
    },
    secret,
    {
      algorithm: 'HS256',
      expiresIn: '1m' // 1 minute expiry
    }
  )
}

export function verifyWebhookToken(token: string): boolean {
  const secret = process.env.CHHLAT_DB_WEBHOOK_TOKEN
  if (!secret) {
    throw new Error('CHHLAT_DB_WEBHOOK_TOKEN environment variable is required')
  }

  try {
    jwt.verify(token, secret, {
      algorithms: ['HS256']
    })
    return true
  } catch (error) {
    // Token is invalid, expired, or malformed
    return false
  }
}
```

### Usage in API Routes
```typescript
// Generate JWT token for webhook requests
import { generateWebhookToken } from '@/utils/jwt'

const jwtToken = generateWebhookToken()

// Use in webhook request
const response = await fetch(webhookUrl, {
  method: 'POST',
  headers: { 
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${jwtToken}`
  },
  body: JSON.stringify({
    operation: 'SELECT',
    sql: 'SELECT * FROM users WHERE id = $1',
    params: [authId]
  })
})
```

## Middleware Authentication

### Route Protection
```typescript
// /src/middleware.ts
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()
  const pathname = req.nextUrl.pathname

  // Skip auth check for public routes
  if (pathname.startsWith('/auth') || pathname === '/' || pathname.startsWith('/api/auth')) {
    return res
  }

  // Check authentication for protected routes
  const supabase = createMiddlewareClient({ req, res })
  const { data: { session } } = await supabase.auth.getSession()

  // Redirect to login if not authenticated
  if (!session && pathname.startsWith('/dashboard')) {
    return NextResponse.redirect(new URL('/auth/login', req.url))
  }

  // Redirect to dashboard if authenticated and trying to access auth pages
  if (session && pathname.startsWith('/auth')) {
    return NextResponse.redirect(new URL('/dashboard', req.url))
  }

  return res
}

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
```

## Security Patterns

### User-Scoped Operations
```typescript
// Always scope operations to the authenticated user using PostgreSQL utilities
import { queryOne, query } from '@/lib/postgres'

const getUserData = async (authId: string) => {
  const sql = `
    SELECT * FROM clients 
    WHERE auth_id = $1
  `
  
  return await queryOne(sql, [authId])
}

// User-scoped operations with client_id (most common pattern)
const getUserPhotos = async (clientId: string) => {
  const sql = `
    SELECT * FROM photos 
    WHERE client_id = $1
    ORDER BY created_at DESC
  `
  
  return await query(sql, [clientId])
}

// Never allow unscoped queries
// BAD: SELECT * FROM photos WHERE photo_id = $1
// GOOD: SELECT * FROM photos WHERE client_id = $1 AND photo_id = $2
```

### File Path Validation
```typescript
// Validate file paths belong to authenticated user
const validateUserFilePath = (filePath: string, authId: string) => {
  const allowedPrefixes = [
    `photos/${authId}/`,
    `audios/${authId}/`,
    `documents/${authId}/`
  ]
  
  return allowedPrefixes.some(prefix => filePath.startsWith(prefix))
}

// Usage in API routes
const filePaths = request.filePaths
const invalidPaths = filePaths.filter(path => 
  !validateUserFilePath(path, authId)
)

if (invalidPaths.length > 0) {
  return NextResponse.json({
    success: false,
    error_msg: 'Invalid file paths detected'
  }, { status: 403 })
}
```

### Session Management
```typescript
// Secure session handling
const handleAuthStateChange = (event: string, session: Session | null) => {
  switch (event) {
    case 'SIGNED_IN':
      // Clear any cached data from previous sessions
      clearUserCache()
      // Set up user-specific data
      initializeUserData(session.user.id)
      break
      
    case 'SIGNED_OUT':
      // Clear all user data and caches
      clearUserCache()
      clearServerCache()
      // Redirect to login
      window.location.href = '/auth/login'
      break
      
    case 'TOKEN_REFRESHED':
      // Update any cached user data
      updateUserSession(session)
      break
  }
}
```

## Authentication Flow

### Sign In Flow
```typescript
// 1. User submits credentials
const handleSignIn = async (email: string, password: string) => {
  try {
    setLoading(true)
    setError(null)
    
    // 2. Authenticate with Supabase
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    
    if (error) throw error
    
    // 3. Update auth context
    setSession(data.session)
    setUser(data.user)
    
    // 4. Redirect to dashboard
    router.push('/dashboard')
    
  } catch (error) {
    setError(error.message)
  } finally {
    setLoading(false)
  }
}
```

### Sign Out Flow
```typescript
// 1. Clear client-side state
const handleSignOut = async () => {
  try {
    setLoading(true)
    
    // 2. Clear server-side cache
    await fetch('/api/auth/clear-cache', { method: 'POST' })
    
    // 3. Sign out from Supabase
    const { error } = await supabase.auth.signOut()
    if (error) throw error
    
    // 4. Clear local state
    setSession(null)
    setUser(null)
    
    // 5. Redirect to home
    router.push('/')
    
  } catch (error) {
    console.error('Sign out error:', error)
  } finally {
    setLoading(false)
  }
}
```

## Error Handling

### Authentication Errors
```typescript
// Handle different types of auth errors
const handleAuthError = (error: any) => {
  if (error.message.includes('Invalid login credentials')) {
    return 'Invalid email or password'
  }
  
  if (error.message.includes('Email not confirmed')) {
    return 'Please confirm your email address'
  }
  
  if (error.message.includes('Too many requests')) {
    return 'Too many login attempts. Please try again later'
  }
  
  return 'Authentication failed. Please try again'
}
```

### Token Expiration
```typescript
// Handle token expiration
const handleTokenExpiration = async () => {
  try {
    // Attempt to refresh token
    const { data, error } = await supabase.auth.refreshSession()
    
    if (error) {
      // Force sign out if refresh fails
      await supabase.auth.signOut()
      window.location.href = '/auth/login'
      return
    }
    
    // Update session with new token
    setSession(data.session)
    
  } catch (error) {
    console.error('Token refresh error:', error)
    // Force sign out on refresh failure
    await supabase.auth.signOut()
    window.location.href = '/auth/login'
  }
}
```

## Cache Management

### Clear User Cache on Auth Changes
```typescript
// Clear cache when user signs out or changes
const clearUserCache = () => {
  // Clear client-side cache
  localStorage.removeItem('dashboard-cache')
  localStorage.removeItem('knowledge-cache')
  
  // Clear server-side cache
  fetch('/api/auth/clear-cache', { method: 'POST' })
    .catch(error => console.warn('Failed to clear server cache:', error))
}
```

### Cache Invalidation API
```typescript
// /src/app/api/auth/clear-cache/route.ts
export async function POST(request: Request) {
  try {
    const { authenticated, authId, clientId } = await verifyAuth()
    
    if (authenticated && authId) {
      // Clear user-specific cache entries
      serverCache.delete(`dashboard_${authId}`)
      serverCache.delete(`knowledge_${authId}`)
      
      // Log client information for debugging
      console.log(`Cache cleared for authId: ${authId}, clientId: ${clientId}`)
    }
    
    return NextResponse.json({
      success: true,
      message: 'Cache cleared successfully'
    })
    
  } catch (error) {
    return NextResponse.json({
      success: false,
      error_msg: 'Failed to clear cache'
    }, { status: 500 })
  }
}
```

## Best Practices

1. **Always verify authentication** in API routes
2. **Use middleware** for route protection
3. **Scope all operations** to authenticated users
4. **Validate file paths** for security
5. **Handle auth errors** gracefully
6. **Clear cache** on authentication changes
7. **Use short-lived tokens** for webhook authentication
8. **Implement proper session management**
9. **Handle token expiration** automatically
10. **Test authentication flows** thoroughly

## Environment Variables

```bash
# Supabase configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# JWT configuration
CHHLAT_DB_WEBHOOK_TOKEN=your-webhook-jwt-secret

# Database webhook
CHHLAT_DB_WEBHOOK_URL=your-webhook-url
```

## Testing Authentication

### Unit Tests
```typescript
// Test auth verification
describe('verifyAuth', () => {
  it('should return authenticated user', async () => {
    const result = await verifyAuth()
    expect(result.authenticated).toBe(true)
    expect(result.authId).toBeDefined()
  })

  it('should return unauthenticated for invalid session', async () => {
    // Mock invalid session
    const result = await verifyAuth()
    expect(result.authenticated).toBe(false)
    expect(result.authId).toBeNull()
  })
})
```

### Integration Tests
```typescript
// Test API authentication
describe('API Authentication', () => {
  it('should reject unauthenticated requests', async () => {
    const response = await fetch('/api/protected-endpoint')
    expect(response.status).toBe(401)
  })

  it('should accept authenticated requests', async () => {
    const response = await fetch('/api/protected-endpoint', {
      headers: { 'Authorization': `Bearer ${validToken}` }
    })
    expect(response.status).toBe(200)
  })
})
```