'use client'

import { useState, useEffect, useCallback } from 'react'
import { usePathname } from 'next/navigation'
import { LinkButton, Button, AnswerEditField, DashboardHeader } from '@/components/ui';
import { motion } from 'framer-motion'
import DashboardFooter from '@/components/DashboardFooter'
import KnowledgeTopSection from '@/components/ui/knowledge/KnowledgeTopSection';
import HelpSection from '@/components/ui/knowledge/HelpSection';
import { PhotoSearchBar, SelectedPhotoDisplay, UpdateStatusOverlay } from '@/components/ui/knowledge';
import { 
  EditModal,
  UpdateConfirmationModal,
  CancelConfirmationModal,
  ImageGalleryModal,
  AudioModeWarningModal,
  DeleteAudioConfirmationModal
} from '@/components/ui/modals';


import { useKnowledgeData } from '@/hooks/useOptimizedData'
import { FaComments } from 'react-icons/fa';

import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'

// TypeScript interfaces
interface PhotoData {
  id: number
  photo_id: string
  photo_url: string[] | null
  photo_file_path?: string[] | null
}

interface SelectedPhoto {
  id: number
  photo_id: string
  photo_url: string | null
  full_photo_urls: string[] | null
}

interface ImageGallery {
  urls: string[]
  currentIndex: number
}

interface EditingItem {
  value: string
}



export default function IntroPage() {
  const pathname = usePathname()
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()
  
  // Use knowledge cache for statistics and client info
  const { 
    data: knowledgeData, 
    loading: isKnowledgeLoading,
  } = useKnowledgeData()
  const knowledgeStats = knowledgeData?.knowledgeStats
  const clientLang = knowledgeData?.clientLang
  const photosData = knowledgeData?.photos || []

  // Get knowledge stats from knowledge cache (real data)
  const totalFaqs = knowledgeStats?.faqCount || 0
  const photoCount = knowledgeStats?.photoCount || 0
  const totalFaqsLimit = knowledgeStats?.faqLimit || 0
  const photoLimit = knowledgeStats?.photoLimit || 0

  const isLoadingCount = isKnowledgeLoading

  // Welcome data state (fetched fresh each time)
  const [welcomeData, setWelcomeData] = useState<any[]>([])
  const [isLoadingWelcome, setIsLoadingWelcome] = useState(true)

  // Fetch welcome data function
  const fetchWelcomeData = useCallback(async () => {
    try {
      setIsLoadingWelcome(true)
      
      const response = await fetch('/api/knowledge/welcome-chat')
      let responseData = await response.json()
      
      // Handle array response format from N8N webhook
      if (Array.isArray(responseData) && responseData.length > 0) {
        responseData = responseData[0]
      }
      
      if (!response.ok || !responseData.success) {
        console.error('Error fetching welcome data:', responseData.error_msg)
        setWelcomeData([])
        return
      }

      // Handle both array and single object responses
      const isArray = Array.isArray(responseData.body)
      const welcomeDataArray = isArray 
        ? responseData.body || []
        : responseData.body 
          ? [responseData.body] 
          : []
      
      setWelcomeData(welcomeDataArray)
    } catch (error) {
      console.error('Error fetching welcome data:', error)
      setWelcomeData([])
    } finally {
      setIsLoadingWelcome(false)
    }
  }, [])

  // Update welcome data in state after save
  const updateWelcomeDataInState = useCallback((updatedRecord: any) => {
    setWelcomeData(prevData => 
      prevData.map(record => 
        record.chat_id === updatedRecord.chat_id ? updatedRecord : record
      )
    )
  }, [])

  // Photo search state
  const [photoSearchQuery, setPhotoSearchQuery] = useState('')
  const [photoSearchResults, setPhotoSearchResults] = useState<PhotoData[]>([])
  const [showPhotoResults, setShowPhotoResults] = useState(false)
  const [isSearching, setIsSearching] = useState(false)
  const [selectedPhoto, setSelectedPhoto] = useState<SelectedPhoto | null>(null)

  // Loading animation states for photo selection
  const [isPhotoLoading, setIsPhotoLoading] = useState(false)



  // Image gallery state
  const [imageGallery, setImageGallery] = useState<ImageGallery | null>(null)





  // Text content state
  const [introText, _setIntroText] = useState('')
  const [isAudioAnswer, setIsAudioAnswer] = useState(false)
  
  // Audio validation state
  const [isValidatingAudio, setIsValidatingAudio] = useState<boolean>(false)
  const [audioValidation, setAudioValidation] = useState<{valid: boolean, duration?: number, file_id?: string, previewUrl?: string, error?: string} | null>(null)
  const [showAudioModeWarning, setShowAudioModeWarning] = useState(false)
  const [showDeleteAudioConfirm, setShowDeleteAudioConfirm] = useState(false)

  // Audio playback state for intro section (isolated)
  const [introAudioId, setIntroAudioId] = useState<string | null>(null)
  const [isIntroAudioPlaying, setIsIntroAudioPlaying] = useState(false)
  const [introAudioUrl, setIntroAudioUrl] = useState<string | null>(null)

  // Custom setters to track changes
  const setIntroText = (value: string) => {
    _setIntroText(value);
  }

  const [isIntroSaving, setIsIntroSaving] = useState(false)

  // Loading state for intro data
  const [isLoadingIntroData, setIsLoadingIntroData] = useState(true)

  // Edit mode state
  const [isIntroEditing, setIsIntroEditing] = useState(false)
  const [showSaveConfirmation, setShowSaveConfirmation] = useState(false)
  const [showCancelConfirmation, setShowCancelConfirmation] = useState(false)
  // Removed confirmationSection since we only have intro
  const [saveStatus, setSaveStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  const [updateProgress, setUpdateProgress] = useState(0)
  const [updateMessage, setUpdateMessage] = useState('')

  // Initial state tracking for detecting changes
  const [initialIntroText, setInitialIntroText] = useState('')
  const [initialSelectedIntroPhoto, setInitialSelectedIntroPhoto] = useState<SelectedPhoto | null>(null)
  const [initialIsAudioAnswer, setInitialIsAudioAnswer] = useState(false)
  const [initialIntroAudioUrl, setInitialIntroAudioUrl] = useState<string | null>(null)

  // Store actual chat_ids from database
  const [introChatId, setIntroChatId] = useState<string | null>(null)
  const [hasIntroChanges, setHasIntroChanges] = useState(false)
  
  // Track original audio data for change detection and cleanup
  const [originalAudioCode, setOriginalAudioCode] = useState<string | null>(null)
  const [originalAudioFilePath, setOriginalAudioFilePath] = useState<string | null>(null)

  // Editing state
  const [editingItem, setEditingItem] = useState<EditingItem | null>(null)

  // Track if textarea has been focused for mobile behavior
  const [hasFocusedInput, setHasFocusedInput] = useState(false)
  
  // Request deduplication for save operations
  const [isSaveInProgress, setIsSaveInProgress] = useState(false)

  // Change detection functions following the comprehensive rules
  const hasAnswerChanged = useCallback(() => {
    const finalTextAnswer = isAudioAnswer ? '' : introText
    const originalTextAnswer = initialIsAudioAnswer ? '' : initialIntroText
    
    return Boolean(
      // Text content changed
      finalTextAnswer !== originalTextAnswer ||
      // Text was added (original had no text but now has text)
      (!originalTextAnswer && finalTextAnswer?.trim()) ||
      // Text was removed (original had text but now empty)
      (originalTextAnswer && !finalTextAnswer?.trim())
    )
  }, [isAudioAnswer, introText, initialIsAudioAnswer, initialIntroText])

  const hasAudioModeChanged = useCallback(() => {
    return isAudioAnswer !== initialIsAudioAnswer
  }, [isAudioAnswer, initialIsAudioAnswer])

  const hasAudioCodeChanged = useCallback(() => {
    return isAudioAnswer && introText && introText.trim() !== originalAudioCode
  }, [isAudioAnswer, introText, originalAudioCode])

  const hasAudioChanged = useCallback(() => {
    return hasAudioModeChanged() || hasAudioCodeChanged()
  }, [hasAudioModeChanged, hasAudioCodeChanged])

  const hasPhotoChanged = useCallback(() => {
    const originalPhotoUrl = initialSelectedIntroPhoto?.full_photo_urls
    const currentPhotoUrl = selectedPhoto?.full_photo_urls
    
    return (
      // Photo added (didn't exist before but exists now)
      (selectedPhoto && !originalPhotoUrl) ||
      // Photo removed (existed before but not now)
      (!selectedPhoto && originalPhotoUrl) ||
      // Photo changed (different photo ID)
      (selectedPhoto && originalPhotoUrl &&
        initialSelectedIntroPhoto?.photo_id !== selectedPhoto.photo_id)
    )
  }, [selectedPhoto, initialSelectedIntroPhoto])

  // Check if only photo has changed (text and audio untouched)
  const isOnlyPhotoChanged = useCallback(() => {
    return hasPhotoChanged() && !hasAnswerChanged() && !hasAudioChanged()
  }, [hasPhotoChanged, hasAnswerChanged, hasAudioChanged])

  // Check if there are any real changes to save
  const hasRealChanges = useCallback(() => {
    return hasAnswerChanged() || hasAudioChanged() || hasPhotoChanged()
  }, [hasAnswerChanged, hasAudioChanged, hasPhotoChanged])

  // Clear photo search
  const clearPhotoSearch = () => {
    setPhotoSearchResults([]);
    setShowPhotoResults(false);
    setPhotoSearchQuery('');
  };

  // Function to validate audio code
  const validateAudioCode = async (audioCode: string) => {
    if (!audioCode.trim()) {
      setAudioValidation(null)
      return
    }

    setIsValidatingAudio(true)
    try {
      const response = await fetch('/api/audio/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ audioCode: audioCode.trim() })
      })

      if (response.ok && response.headers.get('X-Audio-Valid') === 'true') {
        // Response is processed audio blob
        const audioBlob = await response.blob()
        const previewUrl = URL.createObjectURL(audioBlob)

        const duration = parseInt(response.headers.get('X-Audio-Duration') || '0')
        const fileId = response.headers.get('X-Audio-FileId') || ''

        if (duration > 0) {
          setAudioValidation({
            valid: true,
            duration: duration,
            file_id: fileId,
            previewUrl: previewUrl
          })
        } else {
          // Invalid duration
          URL.revokeObjectURL(previewUrl)
          setAudioValidation(null)
          setIntroText('')
          setUpdateMessage('Audio code incorrect or expired')
          setSaveStatus('error')
          setTimeout(() => setSaveStatus('idle'), 2000)
        }
      } else {
        // Handle JSON error response
        const errorData = await response.json()
        console.error('Audio validation failed:', errorData.error_msg)

        // Clear back to normal audio mode
        setAudioValidation(null)
        setIntroText('')
        setUpdateMessage('Audio code incorrect or expired')
        setSaveStatus('error')
        setTimeout(() => setSaveStatus('idle'), 2000)
      }
    } catch (error) {
      console.error('Audio validation error:', error)
      // Clear back to normal audio mode on error
      setAudioValidation(null)
      setIntroText('')
      // Show error message for 2000ms
      setUpdateMessage('Audio code incorrect or expired')
      setSaveStatus('error')
      setTimeout(() => setSaveStatus('idle'), 2000)
    } finally {
      setIsValidatingAudio(false)
    }
  }




  // Search photos
  const searchPhotos = async (query: string) => {
    if (!query.trim()) {
      setPhotoSearchResults([]);
      setShowPhotoResults(false);
      return;
    }

    setIsSearching(true);
    try {
      // Use cached photos data
      if (photosData) {
        const filteredPhotos = photosData
          .filter(photo =>
            photo.photo_id.toLowerCase().includes(query.toLowerCase())
          )
          .slice(0, 5)

        setPhotoSearchResults(filteredPhotos)
        setShowPhotoResults(filteredPhotos.length > 0)
      } else {
        // If no cached data, search via API route (same as main knowledge page)
        const searchResponse = await fetch('/api/knowledge/search-photos', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ query, limit: 5 }),
        })

        let searchData = await searchResponse.json()
        
        // Handle array response format from N8N webhook
        if (Array.isArray(searchData) && searchData.length > 0) {
          searchData = searchData[0]
        }

        if (!searchResponse.ok) {
          console.error('Error searching photos:', searchData.error_msg)
          return
        }

        // Use the raw SQL result directly (no transformation needed)
        const filteredPhotos = searchData.body || []

        setPhotoSearchResults(filteredPhotos)
        setShowPhotoResults(filteredPhotos.length > 0)
      }
    } catch (error) {
      console.error('Error in searchPhotos:', error)
    } finally {
      setIsSearching(false)
    }
  };



  // Handle selecting a photo from search results
  const handleSelectPhoto = (photo: PhotoData) => {
    // Get first photo URL as thumbnail for display, but store the full array
    const thumbnail = photo.photo_url && photo.photo_url.length > 0 ? photo.photo_url[0] : null;
    
    // Clear search results and hide dropdown
    clearPhotoSearch();

    const processedPhoto = {
      id: photo.id,
      photo_id: photo.photo_id,
      photo_url: thumbnail,
      full_photo_urls: photo.photo_url // Store the complete array of photo URLs
    };

    // Show loading animation
    setIsPhotoLoading(true);

    // Hide dropdown and clear search query immediately
    setShowPhotoResults(false);
    setPhotoSearchQuery(''); // Clear the search query after selection

    // Add a small delay to show the loading animation
    setTimeout(() => {
      // Set the selected photo
      setSelectedPhoto(processedPhoto);

      // Update change tracking
      const initialPhotoId = initialSelectedIntroPhoto?.photo_id;
      setHasIntroChanges(
        initialPhotoId !== photo.photo_id ||
        introText !== initialIntroText ||
        isAudioAnswer !== initialIsAudioAnswer
      );

      // Hide loading animation after a short delay
      setTimeout(() => {
        setIsPhotoLoading(false);
      }, 50);
    }, 100);
  };



  // Clear selected photo
  const handleClearSelectedPhoto = () => {
    setSelectedPhoto(null);
    clearPhotoSearch();

    // Update change tracking
    const hadInitialPhoto = initialSelectedIntroPhoto !== null;
    setHasIntroChanges(
      hadInitialPhoto ||
      introText !== initialIntroText ||
      isAudioAnswer !== initialIsAudioAnswer
    );
  };

  // View image in gallery
  const handleViewImage = (urls: string[] | null) => {
    if (!urls || urls.length === 0) {
      console.error('No images available to view');
      return;
    }

    // Filter out any invalid URLs
    const validUrls = urls.filter(url => url && url.trim() !== '');

    if (validUrls.length === 0) {
      console.error('No valid images found to preview');
      return;
    }

    // Open the gallery modal
    setImageGallery({
      urls: validUrls,
      currentIndex: 0
    });
  };

  // Navigate to previous image
  const showPreviousImage = useCallback(() => {
    if (!imageGallery || imageGallery.urls.length <= 1) return;

    const newIndex = (imageGallery.currentIndex - 1 + imageGallery.urls.length) % imageGallery.urls.length;
    setImageGallery({
      ...imageGallery,
      currentIndex: newIndex
    });
  }, [imageGallery]);

  // Navigate to next image
  const showNextImage = useCallback(() => {
    if (!imageGallery || imageGallery.urls.length <= 1) return;

    const newIndex = (imageGallery.currentIndex + 1) % imageGallery.urls.length;
    setImageGallery({
      ...imageGallery,
      currentIndex: newIndex
    });
  }, [imageGallery]);




















  // Handle intro audio click for playback
  const handleIntroAudioClick = () => {
    if (introAudioId === 'intro' && isIntroAudioPlaying) {
      // Stop playing
      setIsIntroAudioPlaying(false);
    } else {
      // Start playing
      setIntroAudioId('intro');
      setIsIntroAudioPlaying(true);
    }
  };

  // Function to confirm switching to audio mode
  const confirmAudioMode = () => {
    setIntroText('');
    setIsAudioAnswer(true);
    setShowAudioModeWarning(false);
    // Clean up any existing audio validation
    if (audioValidation?.previewUrl) {
      URL.revokeObjectURL(audioValidation.previewUrl);
    }
    setAudioValidation(null);
  };

  // Handle audio delete confirmation for validated codes
  const handleConfirmAudioDelete = () => {
    if (audioValidation?.previewUrl) {
      URL.revokeObjectURL(audioValidation.previewUrl)
    }
    setAudioValidation(null)
    setIntroText('')
    setShowDeleteAudioConfirm(false)
    // Note: Stay in audio mode (don't change isAudioAnswer) - same as knowledgeBase page
  }

  // Audio playback handlers for validated audio codes
  const handleValidatedAudioClick = () => {
    // For validated audio, we use a special ID
    const validatedAudioId = 'validated-audio-intro';
    if (introAudioId === validatedAudioId) {
      // Same audio clicked - toggle play/pause
      setIsIntroAudioPlaying(!isIntroAudioPlaying);
    } else {
      // Start playing validated audio
      setIntroAudioId(validatedAudioId);
      setIsIntroAudioPlaying(true);
    }
  };

  // Handle text input for intro
  const handleTextChange = (value: string) => {
    setIntroText(value);
    // If this is an answer edit and we're in audio mode, validate the new code
    if (isAudioAnswer && value.trim()) {
      validateAudioCode(value.trim());
    }
    // Update change tracking
    setHasIntroChanges(
      value !== initialIntroText ||
      isAudioAnswer !== initialIsAudioAnswer ||
      (selectedPhoto !== initialSelectedIntroPhoto)
    );
  };

  // Show save confirmation popup
  const showSaveConfirmationPopup = () => {
    // Check if there's valid content before showing the confirmation
    const hasValidContent = hasValidIntroContent();
    const hasChanges = hasIntroChanges;

    if (!hasValidContent) {
      console.warn(`Cannot save intro without text content`);
      return;
    }

    if (!hasChanges) {
      console.warn(`No changes to save for intro`);
      return;
    }

    setShowSaveConfirmation(true);
  };

  // Check if intro has changes
  const checkIntroChanges = (): boolean => {
    // Check text changes (only if not in audio mode or if audio file exists)
    if (!introAudioUrl && introText !== initialIntroText) return true;

    // Check audio state changes
    if (isAudioAnswer !== initialIsAudioAnswer) return true;

    // Check if audio file was removed
    if (initialIsAudioAnswer && introAudioUrl === null) return true;

    // Check photo changes - more comprehensive detection
    const initialPhotoId = initialSelectedIntroPhoto?.photo_id;
    const currentPhotoId = selectedPhoto?.photo_id;
    
    // Photo was added (had none, now has one)
    if (!initialPhotoId && currentPhotoId) return true;
    
    // Photo was removed (had one, now has none)
    if (initialPhotoId && !currentPhotoId) return true;
    
    // Photo was changed (different photo selected)
    if (initialPhotoId && currentPhotoId && initialPhotoId !== currentPhotoId) return true;

    return false;
  };



  // Check if intro has valid content (text, valid audio, or existing audio file)
  const hasValidIntroContent = (): boolean => {
    // If there's an existing audio file, consider it valid
    if (introAudioUrl) {
      return true;
    }

    if (isAudioAnswer) {
      // For audio answers, check if validation passed
      return audioValidation?.valid || false;
    }
    return introText.trim() !== '';
  };

  // Disable page scroll when any popup/modal is open and pause audio
  useEffect(() => {
    const hasAnyPopup = editingItem || showSaveConfirmation || showCancelConfirmation ||
                       showAudioModeWarning || showDeleteAudioConfirm || imageGallery ||
                       saveStatus === 'loading' || saveStatus === 'success' || saveStatus === 'error';

    if (hasAnyPopup) {
      document.body.style.overflow = 'hidden';
      // Pause any playing audio when modal opens
      setIntroAudioId(null);
      setIsIntroAudioPlaying(false);
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [editingItem, showSaveConfirmation, showCancelConfirmation, showAudioModeWarning, showDeleteAudioConfirm, imageGallery, saveStatus]);

  // Cleanup audio validation preview URL on unmount
  useEffect(() => {
    return () => {
      if (audioValidation?.previewUrl) {
        URL.revokeObjectURL(audioValidation.previewUrl);
      }
    };
  }, [audioValidation?.previewUrl]);



  // Reset intro to initial state
  const resetIntroToInitial = () => {
    // Reset text to initial value
    setIntroText(initialIntroText);
    setSelectedPhoto(initialSelectedIntroPhoto);
    setIsAudioAnswer(initialIsAudioAnswer);
    setIntroAudioUrl(initialIntroAudioUrl);
    setPhotoSearchQuery(''); // Reset search query
    setHasIntroChanges(false);
    // Reset audio validation and playback state
    setAudioValidation(null);
    setIntroAudioId(null);
    setIsIntroAudioPlaying(false);
  };



  // Handle cancel edit
  const handleCancelEdit = () => {
    const hasChanges = checkIntroChanges();

    if (hasChanges) {
      // Show confirmation dialog
      setShowCancelConfirmation(true);
    } else {
      // No changes, just exit edit mode
      setIsIntroEditing(false);
    }
  };

  // Confirm cancel with reset
  const confirmCancel = () => {
    // Clean up audio validation preview URL
    if (audioValidation?.previewUrl) {
      URL.revokeObjectURL(audioValidation.previewUrl);
    }
    setAudioValidation(null);

    // Use the normal reset function
    resetIntroToInitial();
    setIsIntroEditing(false);

    // Close the confirmation dialog
    setShowCancelConfirmation(false);
  };





  // Handle edit button click
  const handleEdit = () => {
    // Save initial state for tracking changes
    setInitialIntroText(introText);
    setInitialSelectedIntroPhoto(selectedPhoto ? {...selectedPhoto} : null);
    setInitialIsAudioAnswer(isAudioAnswer);
    setInitialIntroAudioUrl(introAudioUrl);
    setHasIntroChanges(false);
    setIsIntroEditing(true);
  };




  // Save intro settings to welcome_chat table
  const handleSave = async () => {
    // Prevent concurrent save operations
    if (isSaveInProgress) {
      console.warn('Save operation already in progress');
      return;
    }

    // Close the confirmation dialog
    setShowSaveConfirmation(false);

    // Set the appropriate saving state
    setIsSaveInProgress(true);
    setIsIntroSaving(true);
    setIsIntroEditing(false);


    // Set initial loading state
    setSaveStatus('loading');
    setUpdateProgress(0);
    setUpdateMessage(`Preparing to save intro message...`);

    try {

      // Use the actual chat_id from database
      const chatId = introChatId;

      if (!chatId) {
        console.error(`No intro chat_id found. Record may not exist.`);
        setUpdateMessage(`Intro record not found. Please refresh the page.`);
        setSaveStatus('error');
        return;
      }

      // Validate that we have actual changes to save
      if (!hasRealChanges()) {
        setUpdateMessage('No changes to save');
        setSaveStatus('error');
        setTimeout(() => setSaveStatus('idle'), 2000);
        return;
      }

      // Update progress
      setUpdateProgress(20);
      setUpdateMessage('Processing changes...');

      // For audio mode, validate audio code first
      if (isAudioAnswer && hasAudioChanged()) {
        if (!audioValidation?.valid) {
          setUpdateMessage('Please provide a valid audio code');
          setSaveStatus('error');
          setTimeout(() => setSaveStatus('idle'), 3000);
          return;
        }
      }

      setUpdateProgress(40);

      // Prepare updateData following the old pattern
      const imageUrl = selectedPhoto?.full_photo_urls
      const imageName = selectedPhoto?.photo_id
      
      const updateData: Record<string, unknown> = {
        photo_url: imageUrl === undefined ? null : imageUrl,
        photo_id: imageName === undefined ? null : imageName,
        is_audio: isAudioAnswer,
        onlyPhoto: isOnlyPhotoChanged()
      };

      // Conditional fields based on changes
      if (hasAnswerChanged()) {
        updateData.answer_p = isAudioAnswer ? '' : introText;
      }

      if (hasAudioChanged()) {
        updateData.isAudioAnswer = isAudioAnswer;
        updateData.audioCode = isAudioAnswer && introText ? introText.trim() : null;
        updateData.hasAudioCodeChanged = hasAudioCodeChanged();
        updateData.originalAudioFilePath = originalAudioFilePath;
        // Include audio duration for new audio codes
        updateData.audio_duration = isAudioAnswer && audioValidation?.valid ? audioValidation.duration : null;
      }

      if (hasPhotoChanged()) {
        updateData.fb_photo_atmid = null;
        updateData.ig_photo_atmid = null;
        updateData.tg_photo_atmid = null;
      }

      setUpdateProgress(70);
      setUpdateMessage('Saving to database...');

      setUpdateProgress(80);

      // Always update existing record (intro generated during registration)
      setUpdateMessage('Updating record...');
      try {
        // Prepare request body - use FormData if audio blob exists, otherwise JSON
        let requestBody: FormData | string;
        let requestHeaders: Record<string, string>;

        if (isAudioAnswer && audioValidation?.valid && audioValidation.previewUrl && hasAudioChanged()) {
          // Convert preview URL to blob for upload
          const audioResponse = await fetch(audioValidation.previewUrl);
          const audioBlob = await audioResponse.blob();

          // Use FormData for audio upload
          const formData = new FormData();
          formData.append('chat_id', chatId);
          formData.append('updateData', JSON.stringify(updateData));
          formData.append('sector', knowledgeData?.sector || '');
          formData.append('lang', knowledgeData?.clientLang || '');
          formData.append('audioBlob', audioBlob, 'audio.m4a');

          requestBody = formData;
          requestHeaders = {}; // Let browser set Content-Type for FormData
        } else {
          // Use JSON for non-audio updates
          requestBody = JSON.stringify({
            chat_id: chatId,
            updateData: updateData,
            sector: knowledgeData?.sector,
            lang: knowledgeData?.clientLang
          });
          requestHeaders = { 'Content-Type': 'application/json' };
        }

        const updateResponse = await fetch('/api/knowledge/welcome-chat', {
          method: 'PUT',
          headers: requestHeaders,
          body: requestBody
        });

        let updateResult = await updateResponse.json();
        
        // Handle array response format from N8N webhook
        if (Array.isArray(updateResult) && updateResult.length > 0) {
          updateResult = updateResult[0]
        }

        if (!updateResponse.ok) {
          throw new Error(updateResult.error || 'Failed to update record');
        }

        // Use the returned audio URL from the API - handle null values for audio-to-text cleanup
        const newAudioUrl = updateResult.body?.audio_url !== undefined ? updateResult.body.audio_url : (isAudioAnswer ? introAudioUrl : null);
        const newAudioFilePath = updateResult.body?.audio_file_path !== undefined ? updateResult.body.audio_file_path : null;

        // Update local state with optimistic UI data
        if (updateWelcomeDataInState) {
          // Find current intro data
          const currentIntroData = welcomeData?.find((record: { chat_id: string }) => record.chat_id.endsWith('-1'));

          // Update with new audio URL - properly handle null values for cleanup
          const updatedRecord = {
            ...currentIntroData,
            audio_url: newAudioUrl,
            audio_file_path: newAudioFilePath
          };

          updateWelcomeDataInState(updatedRecord);
        }

        // Update local intro state to reflect the changes
        setIntroAudioUrl(newAudioUrl);
        setInitialIntroAudioUrl(newAudioUrl);

        // Update original tracking for future change detection
        setOriginalAudioFilePath(newAudioFilePath);
      } catch (error) {
        console.error('Error updating record:', error);
        throw new Error('Failed to update record');
      }

      setUpdateProgress(90);
      setUpdateMessage(`Intro message saved successfully!`);

      // The webhook is now handled automatically by the API endpoint
      // No need for manual webhook triggering here
      setUpdateProgress(100);
      setUpdateMessage(`Intro message saved!`);

      // Reset initial state tracking to reflect the saved state
      setInitialIntroText(introText);
      setInitialSelectedIntroPhoto(selectedPhoto ? {...selectedPhoto} : null);
      setInitialIsAudioAnswer(isAudioAnswer);
      setHasIntroChanges(false);

      // Set success status
      setSaveStatus('success');

      // Auto-hide success message after 1.5 seconds
      setTimeout(() => setSaveStatus('idle'), 1500);

    } catch (error: unknown) {
      console.error('Error saving intro:', error);
      
      // Provide specific error messages based on error type
      let errorMessage = 'Error saving intro message. Please try again.'
      if (error instanceof Error) {
        if (error.message?.includes('Audio code')) {
          errorMessage = 'Invalid audio code. Please check the code and try again.'
        } else if (error.message?.includes('Unauthorized')) {
          errorMessage = 'Session expired. Please refresh the page and try again.'
        } else if (error.message?.includes('Network')) {
          errorMessage = 'Network error. Please check your connection and try again.'
        } else {
          errorMessage = error.message
        }
      }
      
      setUpdateMessage(errorMessage);
      setSaveStatus('error');
      
      // Auto-clear error status after 5 seconds
      setTimeout(() => {
        setSaveStatus('idle');
      }, 5000);
    } finally {
      // Reset the appropriate saving state
      setTimeout(() => {
        setIsIntroSaving(false);
        setIsSaveInProgress(false);
      }, 1000);
    }
  };

  // Load existing intro data from welcome data state
  const loadIntroData = useCallback(async () => {
    try {
      // Wait for welcome data to load
      if (isLoadingWelcome || welcomeData.length === 0) {
        return
      }

      // Set loading state
      setIsLoadingIntroData(true)

      // Get intro data based on chat_id pattern
      const introData = welcomeData?.find((record: { chat_id: string }) => record.chat_id.endsWith('-1'));

      // Set intro data if exists
      if (introData) {
        // Store the actual chat_id from database
        setIntroChatId(introData.chat_id);

        // Load audio state first - determine from presence of audio_url since is_audio_answer field doesn't exist
        const audioAnswerValue = !!(introData.audio_url);
        setIsAudioAnswer(audioAnswerValue);
        setInitialIsAudioAnswer(audioAnswerValue);
        
        // Set audio URL if exists
        if (introData.audio_url) {
          setIntroAudioUrl(introData.audio_url);
          setInitialIntroAudioUrl(introData.audio_url);
        } else {
          setIntroAudioUrl(null);
          setInitialIntroAudioUrl(null);
        }
        
        // Use answer_p field only if not in audio mode (audio mode should show empty text input)
        const introTextValue = audioAnswerValue ? '' : (introData.answer_p || '');
        setIntroText(introTextValue);
        setInitialIntroText(introTextValue);
        
        // Track original audio data for change detection and cleanup
        setOriginalAudioCode(audioAnswerValue ? introTextValue : null);
        setOriginalAudioFilePath(introData.audio_file_path || null);
        
        // Reset audio validation when loading new data
        setAudioValidation(null);



        // Set photo if exists
        if (introData.photo_url && introData.photo_id) {
          const photoData = {
            id: 0, // We don't need the actual ID here
            photo_id: introData.photo_id,
            photo_url: Array.isArray(introData.photo_url) && introData.photo_url.length > 0
              ? introData.photo_url[0]
              : null,
            full_photo_urls: introData.photo_url
          };

          setSelectedPhoto(photoData);
          setInitialSelectedIntroPhoto({...photoData});
        }
      }



      // Reset change tracking
      setHasIntroChanges(false);

    } catch (error) {
      console.error('Error loading intro data:', error);
    } finally {
      // Clear loading state
      setIsLoadingIntroData(false)
    }
  }, [isLoadingWelcome, welcomeData]);

  // Fetch welcome data on mount
  useEffect(() => {
    fetchWelcomeData()
  }, [fetchWelcomeData])

  // Load intro data when welcome data is ready
  useEffect(() => {
    if (!isLoadingWelcome && welcomeData.length >= 0) {
      loadIntroData()
    }
  }, [isLoadingWelcome, welcomeData, loadIntroData])









  // Memoize the handleSaveEdit function
  const handleSaveEdit = useCallback(() => {
    // Use requestAnimationFrame for smoother UI updates
    requestAnimationFrame(() => {
      if (!editingItem) {
        return;
      }

      // Only save if the value actually changed from the initial value
      if (editingItem.value !== introText) {
        handleTextChange(editingItem.value);
      }

      // Close modal after state updates
      setEditingItem(null);
    });
  }, [editingItem, introText]);



  // Reset focus tracking when modal closes
  useEffect(() => {
    if (!editingItem) {
      setHasFocusedInput(false);
    }
  }, [editingItem]);



  // Update change tracking whenever relevant data changes
  useEffect(() => {
    if (isIntroEditing) {
      setHasIntroChanges(checkIntroChanges());
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [introText, selectedPhoto, isIntroEditing, initialIntroText, initialSelectedIntroPhoto]);







  // Disable page scroll when gallery is open and add keyboard navigation
  useEffect(() => {
    if (imageGallery) {
      document.body.style.overflow = 'hidden';

      // Add keyboard navigation for photo gallery
      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === 'ArrowLeft') {
          event.preventDefault();
          showPreviousImage();
        } else if (event.key === 'ArrowRight') {
          event.preventDefault();
          showNextImage();
        } else if (event.key === 'Escape') {
          event.preventDefault();
          setImageGallery(null);
        }
      };

      document.addEventListener('keydown', handleKeyDown);

      return () => {
        document.body.style.overflow = '';
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [imageGallery, showPreviousImage, showNextImage]);

  // Disable page scroll when any popup/modal is open
  useEffect(() => {
    const hasAnyPopup = editingItem || showSaveConfirmation || showCancelConfirmation ||
                       saveStatus === 'loading' || saveStatus === 'success' || saveStatus === 'error' ||
                       isLoadingIntroData || imageGallery || showAudioModeWarning || showDeleteAudioConfirm;

    if (hasAnyPopup) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [editingItem, showSaveConfirmation, showCancelConfirmation, saveStatus,
      isLoadingIntroData, imageGallery, showAudioModeWarning, showDeleteAudioConfirm]);

  return (
    <div className={themeConfig.pageBackground}>
      {/* Background effects */}
      {themeConfig.backgroundEffects}

      {/* Theme-aware Header */}
      <header className="relative">
        <div className="container mx-auto px-3 py-3">
          <div
            className={`relative ${themeConfig.card} rounded-2xl px-4 py-3 border ${themeConfig.border} transition-all duration-300 overflow-hidden`}
            style={theme === 'dark' ? {
              boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
            } : {
              boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
            }}
          >


            {/* Content */}
            <div className="relative z-10 flex justify-center items-center">
              <LinkButton 
                href="/dashboard" 
                variant="ghost" 
                className="p-0 hover:bg-transparent active:scale-95"
              >
                <img
                  src={themeConfig.logo}
                  alt="Chhlat Bot"
                  className="h-8 w-auto transition-transform duration-300 hover:scale-105"
                />
              </LinkButton>
            </div>
          </div>
        </div>
      </header>

      <div className="flex-grow container mx-auto px-4 py-2">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Content header with title and back button */}
          <DashboardHeader 
            backHref="/dashboard"
            titleKey="ai_brain"
          />

          {/* Top Section */}
          <KnowledgeTopSection
            currentPath={pathname}
            totalFaqs={totalFaqs}
            totalFaqsLimit={totalFaqsLimit}
            photoCount={photoCount}
            photoLimit={photoLimit}
            isLoadingCount={isLoadingCount}
          />


          {/* Intro Section */}
          <div
            className={`relative ${themeConfig.card} rounded-2xl p-6 mb-6 border ${themeConfig.border} transition-all duration-300 group overflow-hidden`}
            style={theme === 'dark' ? {
              boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
            } : {
              boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
            }}
          >
            <div className="relative z-10">
            <div className="flex justify-between items-center mb-6">
              <div className="flex items-center">
                <FaComments className={`${themeConfig.text} mr-3 h-5 w-5`} />
                <h2 className={`text-xl font-bold font-title ${themeConfig.text}`}>{t('intro_message')}</h2>
              </div>
              <div className="flex space-x-2">
                {isIntroEditing ? (
                  <>
                    <Button
                      onClick={() => handleCancelEdit()}
                      variant="secondary"
                      size="sm"
                      className="text-xs sm:text-base"
                    >
                      {t('cancel')}
                    </Button>
                    <div className="relative">
                      <Button
                        onClick={() => showSaveConfirmationPopup()}
                        variant="primary"
                        size="sm"
                        className="text-xs sm:text-base"
                        disabled={isIntroSaving || !hasIntroChanges || !hasValidIntroContent()}
                        title={!hasValidIntroContent() ? "Add text before saving" : (!hasIntroChanges ? "No changes to save" : "Save changes")}
                        isLoading={isIntroSaving}
                        loadingText={t('saving')}
                      >
                        {t('save')}
                      </Button>
                      {isIntroEditing && !hasValidIntroContent() && (
                        <div className={`absolute -bottom-6 right-0 text-xs ${themeConfig.errorText} whitespace-nowrap`}>
                          {isAudioAnswer ? 'Add valid audio code' : 'Add text'}
                        </div>
                      )}
                    </div>
                  </> 
                ) : (
                  <Button
                    onClick={() => handleEdit()}
                    variant="secondary"
                    size="sm"
                    className="text-xs sm:text-base"
                  >
                    {t('edit')}
                  </Button>
                )}
              </div>
            </div>
            {/* Help Section */}
            <HelpSection 
              youtubeUrl="#" // TODO: Replace with actual YouTube URL
              message={t('intro_description')}
              clientLang={clientLang || 'English'}
            />

            {/* Loading Animation */}
            {isLoadingIntroData ? (
              <>
                {/* Loading header */}
                <div className="py-8 flex justify-center items-center">
                  <div className="flex flex-col items-center space-y-4">
                    {/* Main loading spinner */}
                    <div className="relative">
                      <div className="w-12 h-12 border-4 border-jade-purple/20 border-t-jade-purple rounded-full animate-spin"></div>
                      <div className="absolute inset-0 w-12 h-12 border-4 border-transparent border-r-jade-purple/40 rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>
                    </div>

                    {/* Loading text with animation */}
                    <div className="text-center">
                      <p className={`${themeConfig.text} text-lg font-medium mb-1`}>{t('loading')}</p>
                    </div>

                    {/* Progress indicator */}
                    <div className={`w-48 h-1 ${themeConfig.skeletonElement} rounded-full overflow-hidden`}>
                      <div className="h-full bg-gradient-to-r from-jade-purple to-jade-purple-dark rounded-full animate-pulse"></div>
                    </div>
                  </div>
                </div>

                {/* Skeleton loading for intro form */}
                <div className="space-y-4">
                  {/* Photo search skeleton */}
                  <div className={`h-10 ${themeConfig.skeletonElement} rounded-lg animate-pulse`}></div>

                  {/* Answer input skeleton */}
                  <div className={`h-12 ${themeConfig.skeletonElement} rounded-lg animate-pulse`} style={{ animationDelay: '0.1s' }}></div>
                </div>
              </>
            ) : (
              <>
                {/* Photo Search Bar */}
                <PhotoSearchBar
                  searchQuery={photoSearchQuery}
                  onSearchChange={(query) => {
                    setPhotoSearchQuery(query);
                    if (query.trim()) {
                      searchPhotos(query);
                    } else {
                      setPhotoSearchResults([]);
                      setShowPhotoResults(false);
                    }
                  }}
                  searchResults={photoSearchResults}
                  isSearching={isSearching}
                  showResults={showPhotoResults}
                  onSelectPhoto={handleSelectPhoto}
                  onFocus={() => {
                    if (photoSearchResults.length > 0) {
                      setShowPhotoResults(true);
                    } else if (photoSearchQuery) {
                      searchPhotos(photoSearchQuery);
                    }
                  }}
                  placeholder={t('search_photo_placeholder')}
                  disabled={!isIntroEditing}
                />

            {/* Selected Photo Display */}
            <SelectedPhotoDisplay
              selectedPhoto={selectedPhoto}
              isLoading={isPhotoLoading}
              onRemovePhoto={handleClearSelectedPhoto}
              onViewImage={handleViewImage}
              disabled={!isIntroEditing}
            />

            {/* Answer Input */}
            <div className="mb-4">
              <AnswerEditField
                value={introText}
                isAudioAnswer={isAudioAnswer}
                audioUrl={introAudioUrl}
                audioValidation={audioValidation}
                isValidating={isValidatingAudio}
                disabled={!isIntroEditing}
                onClick={() => {
                  if (!isIntroEditing) {
                    return;
                  }
                  // Handle click based on current state
                  if (introAudioUrl) {
                    // State A: Audio file exists - trigger playback
                    handleIntroAudioClick();
                  } else if (isAudioAnswer && audioValidation?.valid && audioValidation.previewUrl) {
                    // State B: Valid audio code - trigger preview playback
                    handleValidatedAudioClick();
                  } else {
                    // Text mode or audio code mode - open editor
                    setEditingItem({
                      value: introText
                    });
                  }
                }}
                onAudioToggle={() => {
                  if (!isIntroEditing) {
                    return;
                  }

                  // Button logic based on current state
                  if (introAudioUrl) {
                    // Show confirmation before deleting audio file
                    setShowDeleteAudioConfirm(true);
                  } else if (isAudioAnswer && audioValidation?.valid) {
                    // Show confirmation before clearing validated audio code
                    setShowDeleteAudioConfirm(true);
                  } else if (!isAudioAnswer) {
                    // Switch from text to audio mode
                    if (introText.trim()) {
                      setShowAudioModeWarning(true);
                    } else {
                      setIsAudioAnswer(true);
                    }
                  } else {
                    // Switch from audio to text mode
                    setIsAudioAnswer(false);
                    if (audioValidation?.previewUrl) {
                      URL.revokeObjectURL(audioValidation.previewUrl);
                    }
                    setAudioValidation(null);
                  }
                }}
                audioId={introAudioUrl ? "intro" : "validated-audio-intro"}
                isAudioSelected={introAudioUrl ?
                  introAudioId === 'intro' :
                  introAudioId === 'validated-audio-intro'}
                isAudioPlaying={(introAudioUrl ?
                  introAudioId === 'intro' :
                  introAudioId === 'validated-audio-intro') && isIntroAudioPlaying}
                onAudioClick={() => introAudioUrl ?
                  handleIntroAudioClick() :
                  handleValidatedAudioClick()}
                placeholder={t('enter_welcome_message')}
              />
            </div>
              </>
            )}
            </div>
          </div>

          {/* Edit modal*/}
          <EditModal
            editingItem={editingItem ? {
              id: 1,
              field: 'answer',
              value: editingItem.value
            } : null}
            hasFocusedInput={hasFocusedInput}
            onValueChange={(value) => {
              setEditingItem(prev => prev ? {...prev, value} : null);
            }}
            onInputFocus={() => setHasFocusedInput(true)}
            onSave={handleSaveEdit}
            onClose={() => setEditingItem(null)}
          />

          {/* Save Confirmation Modal */}
          <UpdateConfirmationModal
            showConfirmation={showSaveConfirmation}
            onCancel={() => setShowSaveConfirmation(false)}
            onConfirm={handleSave}
            title={t('save_intro_message')}
            message={t('save_intro_confirmation')}
          />

          {/* Cancel Confirmation Modal */}
          <CancelConfirmationModal
            showCancelConfirmation={showCancelConfirmation}
            onKeepEditing={() => setShowCancelConfirmation(false)}
            onConfirmDiscard={confirmCancel}
          />


          {/* Save Status Overlay */}
          <UpdateStatusOverlay
            updateStatus={saveStatus}
            updateProgress={updateProgress}
            updateMessage={updateMessage}
            onClose={() => setSaveStatus('idle')}
            loadingText={t('saving')}
            completeText={t('complete')}
            successText={t('success')}
            errorText={t('error')}
            spinnerSize="sm"
          />

          {/* Image Gallery Modal */}
          <ImageGalleryModal
            imageGallery={imageGallery}
            onClose={() => setImageGallery(null)}
            onImageChange={(index) => {
              if (imageGallery) {
                setImageGallery({
                  ...imageGallery,
                  currentIndex: index
                });
              }
            }}
            onPrevious={showPreviousImage}
            onNext={showNextImage}
          />

          {/* Audio Mode Warning Modal */}
          <AudioModeWarningModal
            showAudioModeWarning={showAudioModeWarning}
            onCancel={() => setShowAudioModeWarning(false)}
            onConfirm={confirmAudioMode}
          />

          {/* Delete Audio Confirmation Modal */}
          <DeleteAudioConfirmationModal
            showDeleteAudioConfirm={showDeleteAudioConfirm}
            onCancel={() => setShowDeleteAudioConfirm(false)}
            onConfirmDelete={() => {
              if (introAudioUrl) {
                // Delete existing audio file and switch to text mode
                setIntroAudioUrl(null);
                setIntroAudioId(null);
                setIsIntroAudioPlaying(false);
                setIsAudioAnswer(false);
                setOriginalAudioCode(null); // Reset original audio code tracking
              } else {
                // Delete validated audio code
                handleConfirmAudioDelete();
                return; // handleConfirmAudioDelete already closes the modal
              }
              setShowDeleteAudioConfirm(false);
            }}
          />
        </motion.div>
      </div>
      <DashboardFooter />
    </div>
  );
}
