'use client'

import { FaBrain, FaImage } from 'react-icons/fa'
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'
import { LinkButton } from '@/components/ui'

interface KnowledgeTopSectionProps {
  currentPath: string
  totalFaqs: number
  totalFaqsLimit: number | null
  photoCount: number
  photoLimit: number | null
  faqUsagePercentage: number
  photoUsagePercentage: number
  isLoadingCount: boolean
}

export default function KnowledgeTopSection({
  currentPath,
  totalFaqs,
  totalFaqsLimit,
  photoCount,
  photoLimit,
  faqUsagePercentage,
  photoUsagePercentage,
  isLoadingCount
}: KnowledgeTopSectionProps) {
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()

  // Determine active states based on current path
  const isBusinessInsightActive = currentPath === '/dashboard/knowledge'
  const isPhotoGalleryActive = currentPath === '/dashboard/knowledge/photo'
  const isIntrosOutrosActive = currentPath === '/dashboard/knowledge/intro'

  return (
    <div className="grid grid-cols-2 gap-4 sm:gap-6 mb-4 sm:mb-6">
      {/* Left Card: Statistics */}
      <div
        className={`relative ${themeConfig.card} rounded-2xl p-3 sm:p-6 border ${themeConfig.border} transition-all duration-300 group overflow-hidden`}
        style={theme === 'dark' ? {
          boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
        } : {
          boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
        }}>

        <div className="relative z-10">
          {/* Modern Circle Stats Grid */}
          <div className="grid grid-cols-2 gap-2 sm:gap-4">
            {/* Business Insight Stat - Circle Design */}
            <div className="flex flex-col items-center justify-center text-center">
              <div className="relative h-16 w-16 sm:h-24 sm:w-24 mb-2 sm:mb-3">
                {/* Progress Circle - SVG implementation - Stroke only */}
                <svg className="absolute inset-0 h-full w-full" viewBox="0 0 100 100">
                  {/* Outer glow filter */}
                  <defs>
                    <filter id="glow1">
                      <feGaussianBlur stdDeviation="2.5" result="blur" />
                      <feComposite in="SourceGraphic" in2="blur" operator="over" />
                    </filter>
                  </defs>

                  {/* Background circle */}
                  <circle
                    cx="50"
                    cy="50"
                    r="40"
                    fill={themeConfig.statCircleBackground}
                    stroke={themeConfig.statCircleBorder}
                    strokeWidth="3"
                  />

                  {/* Base track */}
                  <circle
                    cx="50"
                    cy="50"
                    r="40"
                    fill="transparent"
                    stroke="rgb(83, 44, 199)"
                    strokeWidth="6"
                    opacity="0.2"
                  />

                  {/* Progress track with glow */}
                  <circle
                    cx="50"
                    cy="50"
                    r="40"
                    fill="transparent"
                    stroke="rgb(116, 90, 231)"
                    strokeWidth="6"
                    strokeDasharray={`${Math.min((faqUsagePercentage / 100) * 251.2, 251.2)} 251.2`}
                    strokeDashoffset="0"
                    transform="rotate(-90 50 50)"
                    filter="url(#glow1)"
                  />
                </svg>

                {/* Icon in Center */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className={`w-10 h-10 sm:w-14 sm:h-14 rounded-full border ${themeConfig.border} flex items-center justify-center`} style={{
                    boxShadow: '0 4px 12px rgba(116, 85, 184, 0.3), 0 2px 4px rgba(0, 0, 0, 0.1)'
                  }}>
                    <FaBrain className="w-5 h-5 sm:w-7 sm:h-7 text-jade-purple/75" />
                  </div>
                </div>
              </div>

              {/* Label */}
              <p className={`${themeConfig.textSecondary} text-[10px] sm:text-xs font-body mb-0 sm:mb-1`}>{t('brain')}</p>

              {/* Count */}
              <p className={`${themeConfig.text} text-xs sm:text-base font-body`}>
                {isLoadingCount ?
                  <span className="flex justify-center">
                    <span className={`w-3 h-3 sm:w-4 sm:h-4 border-2 border-current border-t-transparent rounded-full animate-spin ${themeConfig.text}`}></span>
                  </span>
                  : <>{totalFaqs} <span className={themeConfig.textMuted}>/ {totalFaqsLimit || 0}</span></>
                }
              </p>
            </div>

            {/* Photo Gallery Stat - Circle Design */}
            <div className="flex flex-col items-center justify-center text-center">
              <div className="relative h-16 w-16 sm:h-24 sm:w-24 mb-2 sm:mb-3">
                {/* Progress Circle - SVG implementation - Stroke only */}
                <svg className="absolute inset-0 h-full w-full" viewBox="0 0 100 100">
                  {/* Outer glow filter */}
                  <defs>
                    <filter id="glow2">
                      <feGaussianBlur stdDeviation="2.5" result="blur" />
                      <feComposite in="SourceGraphic" in2="blur" operator="over" />
                    </filter>
                  </defs>

                  {/* Background circle */}
                  <circle
                    cx="50"
                    cy="50"
                    r="40"
                    fill={themeConfig.statCircleBackground}
                    stroke={themeConfig.statCircleBorder}
                    strokeWidth="1"
                  />

                  {/* Base track */}
                  <circle
                    cx="50"
                    cy="50"
                    r="40"
                    fill="transparent"
                    stroke="rgb(83, 44, 199)"
                    strokeWidth="6"
                    opacity="0.2"
                  />

                  {/* Progress track with glow */}
                  <circle
                    cx="50"
                    cy="50"
                    r="40"
                    fill="transparent"
                    stroke="rgb(116, 90, 231)"
                    strokeWidth="6"
                    strokeDasharray="251.2"
                    strokeDashoffset={251.2 - Math.min((photoUsagePercentage / 100) * 251.2, 251.2)}
                    transform="rotate(-90 50 50)"
                    filter="url(#glow2)"
                  />
                </svg>

                {/* Icon in Center */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className={`w-10 h-10 sm:w-14 sm:h-14 rounded-full border ${themeConfig.border} flex items-center justify-center`} style={{
                    boxShadow: '0 4px 12px rgba(116, 85, 184, 0.3), 0 2px 4px rgba(0, 0, 0, 0.1)'
                  }}>
                    <FaImage className="w-5 h-5 sm:w-7 sm:h-7 text-jade-purple/75" />
                  </div>
                </div>
              </div>

              {/* Label */}
              <p className={`${themeConfig.textSecondary} text-[10px] sm:text-xs font-body mb-0 sm:mb-1`}>{t('photo_gallery')}</p>

              {/* Count */}
              <p className={`${themeConfig.text} text-xs sm:text-base font-body`}>
                {isLoadingCount ?
                  <span className="flex justify-center">
                    <span className={`w-3 h-3 sm:w-4 sm:h-4 border-2 border-current border-t-transparent rounded-full animate-spin ${themeConfig.text}`}></span>
                  </span>
                  : <>{photoCount} <span className={themeConfig.textMuted}>/ {photoLimit || 0}</span></>
                }
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Right Card: Action Buttons */}
      <div
        className={`relative ${themeConfig.card} rounded-2xl p-3 sm:p-6 border ${themeConfig.border} ${themeConfig.borderHover} transition-all duration-300 group overflow-hidden`}
        style={theme === 'dark' ? {
          boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
        } : {
          boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
        }}>

        <div className="relative z-10">
          {/* Buttons Grid */}
          <div className="grid grid-cols-1 gap-2 sm:gap-3">
            <LinkButton
              href="/dashboard/knowledge"
              variant={isBusinessInsightActive ? "primary" : "secondary"}
              size="md"
              className="text-xs sm:text-base py-2 sm:py-3 w-full text-center"
              isActive={isBusinessInsightActive}
            >
              {t('business_insight')}
            </LinkButton>

            <LinkButton
              href="/dashboard/knowledge/photo"
              variant={isPhotoGalleryActive ? "primary" : "secondary"}
              size="md"
              className="text-xs sm:text-base py-2 sm:py-3 w-full text-center"
              isActive={isPhotoGalleryActive}
            >
              {t('photo_gallery')}
            </LinkButton>

            <LinkButton
              href="/dashboard/knowledge/intro"
              variant={isIntrosOutrosActive ? "primary" : "secondary"}
              size="md"
              className="text-xs sm:text-base py-2 sm:py-3 w-full text-center"
              isActive={isIntrosOutrosActive}
            >
              {t('intros_outros')}
            </LinkButton>
          </div>
        </div>
      </div>
    </div>
  )
}