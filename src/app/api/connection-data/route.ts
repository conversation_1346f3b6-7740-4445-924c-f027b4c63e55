import { NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { generateWebhookToken } from '@/utils/jwt'
import { serverCache } from '@/lib/cache'

// Pre-built connection limits by plan type
const PLAN_CONNECTION_LIMITS: Record<string, number> = {
  Intern: 1,
  Assistant: 3,
  Manager: 4,
  free: 1 // Default fallback
}

// Define the sanitized credentials type (what we send to client)
type SanitizedCredentials = {
  id: number
  client_id: string
  fb_name?: string
  ig_name?: string
  tg_name?: string
  wa_name?: string
  web_name?: string
  web_domain?: string
  fb_status?: number
  ig_status?: number
  wa_status?: number
  tg_status?: number
  web_status?: number
  tg_id_name?: string
  // Note: We don't expose URLs, tokens, or sensitive identifiers for security
}

type ConnectionDataResponse = {
  limit: number
  plan_type: string
  credentials: SanitizedCredentials
}

export async function GET() {
  try {
    // Single authentication check
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false, 
        body: null, 
        error_msg: 'Unauthorized' 
      }, { status: 401 })
    }

    // Validate client ID
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }

    // Get the secure webhook URL from environment
    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
    
    if (!webhookUrl) {
      console.warn('Secure webhook URL not configured')
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Database webhook not configured'
      }, { status: 500 })
    }

    // Try to get plan_type from dashboard cache first
    const dashboardCacheKey = `dashboard_${authId}`
    const cachedDashboardData = serverCache.get(dashboardCacheKey)
    let planType = 'free' // Default fallback

    if (cachedDashboardData?.clientInfo?.plan_type) {
      planType = cachedDashboardData.clientInfo.plan_type
    } else {
    }

    // Generate JWT token for webhook authentication
    const jwtToken = generateWebhookToken()

    let credentialsData: any[] = []

    // If we have plan_type from cache, only fetch credentials
    if (cachedDashboardData?.clientInfo?.plan_type) {
      // Single webhook call for credentials only
      const credentialsResponse = await fetch(webhookUrl, {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${jwtToken}`
        },
        body: JSON.stringify({
          operation: 'SELECT',
          sql: `SELECT id, client_id, fb_name, ig_name, tg_name, wa_name, web_name, web_domain,
                       fb_status, ig_status, wa_status, tg_status, web_status, tg_id_name
                FROM client_credentials 
                WHERE client_id = $1`,
          params: [clientId]
        })
      })

      if (!credentialsResponse.ok) {
        throw new Error(`Credentials webhook request failed: ${credentialsResponse.status} ${credentialsResponse.statusText}`)
      }

      let credentialsWebhookData = await credentialsResponse.json()
      
      // Handle array response format from N8N webhook
      if (Array.isArray(credentialsWebhookData) && credentialsWebhookData.length > 0) {
        credentialsWebhookData = credentialsWebhookData[0]
      }
      
      if (!credentialsWebhookData.success) {
        throw new Error(credentialsWebhookData.error_msg || 'Credentials data operation failed')
      }

      credentialsData = credentialsWebhookData.body || []
    } else {
      // Fallback: fetch both client and credentials data if no cache
      const [clientResponse, credentialsResponse] = await Promise.all([
        // Get client info (including plan_type)
        fetch(webhookUrl, {
          method: 'POST',
          headers: { 
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${jwtToken}`
          },
          body: JSON.stringify({
            operation: 'SELECT',
            sql: 'SELECT plan_type FROM clients WHERE client_id = $1',
            params: [clientId]
          })
        }),
        
        // Get client credentials
        fetch(webhookUrl, {
          method: 'POST',
          headers: { 
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${jwtToken}`
          },
          body: JSON.stringify({
            operation: 'SELECT',
            sql: `SELECT id, client_id, fb_name, ig_name, tg_name, wa_name, web_name, web_domain,
                         fb_status, ig_status, wa_status, tg_status, web_status, tg_id_name
                  FROM client_credentials 
                  WHERE client_id = $1`,
            params: [clientId]
          })
        })
      ])

      // Check webhook responses
      if (!clientResponse.ok) {
        throw new Error(`Client webhook request failed: ${clientResponse.status} ${clientResponse.statusText}`)
      }
      if (!credentialsResponse.ok) {
        throw new Error(`Credentials webhook request failed: ${credentialsResponse.status} ${credentialsResponse.statusText}`)
      }

      // Parse webhook responses
      let clientWebhookData = await clientResponse.json()
      let credentialsWebhookData = await credentialsResponse.json()
      
      // Handle array response format from N8N webhook
      if (Array.isArray(clientWebhookData) && clientWebhookData.length > 0) {
        clientWebhookData = clientWebhookData[0]
      }
      if (Array.isArray(credentialsWebhookData) && credentialsWebhookData.length > 0) {
        credentialsWebhookData = credentialsWebhookData[0]
      }
      
      if (!clientWebhookData.success) {
        throw new Error(clientWebhookData.error_msg || 'Client data operation failed')
      }
      if (!credentialsWebhookData.success) {
        throw new Error(credentialsWebhookData.error_msg || 'Credentials data operation failed')
      }

      const clientData = clientWebhookData.body || []
      credentialsData = credentialsWebhookData.body || []

      // Get plan_type from client data
      if (clientData && clientData.length > 0) {
        planType = clientData[0].plan_type || 'free'
      }
    }

    // Get connection limit from pre-built constants
    const limit = PLAN_CONNECTION_LIMITS[planType] || PLAN_CONNECTION_LIMITS.free

    // If no credentials record exists, return error - credentials should be created by backend
    if (!credentialsData || credentialsData.length === 0) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'No connection credentials found. Please contact support.'
      }, { status: 404 })
    }

    // Return sanitized existing data
    const existingData = credentialsData[0]
    const sanitizedCredentials: SanitizedCredentials = {
      id: existingData.id,
      client_id: existingData.client_id,
      fb_name: existingData.fb_name,
      ig_name: existingData.ig_name,
      tg_name: existingData.tg_name,
      wa_name: existingData.wa_name,
      web_name: existingData.web_name,
      web_domain: existingData.web_domain,
      fb_status: existingData.fb_status,
      ig_status: existingData.ig_status,
      wa_status: existingData.wa_status,
      tg_status: existingData.tg_status,
      web_status: existingData.web_status,
      tg_id_name: existingData.tg_id_name
    }

    const response: ConnectionDataResponse = {
      limit,
      plan_type: planType,
      credentials: sanitizedCredentials
    }

    return NextResponse.json({
      success: true,
      body: response,
      error_msg: null
    })

  } catch (error) {
    console.error('Error in connection-data API:', error)
    return NextResponse.json({
      success: false,
      body: null,
      error_msg: error instanceof Error ? error.message : 'Internal server error'
    }, { status: 500 })
  }
}
