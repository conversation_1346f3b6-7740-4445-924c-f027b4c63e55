'use client'

import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import TerminalAnimation from './TerminalAnimation'
import { useAuth } from '@/context/AuthContext'
import { useLanguage } from '@/context/LanguageContext'
import Link from 'next/link'
import GlassButton from './ui/GlassButton'

export default function Hero() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })
  const { user } = useAuth()
  const { t } = useLanguage()

  return (
    <section className="section flex flex-col items-center justify-center min-h-screen py-8 pt-32 sm:pt-24 md:pt-48 relative overflow-hidden" ref={ref}>
      {/* Grid pattern */}
      {/* <div className="absolute inset-0 opacity-10">
        <div className="grid grid-cols-12 gap-4 h-full">
          {Array.from({ length: 24 }).map((_, i) => (
            <div key={i} className="h-full w-px bg-white opacity-10" style={{ left: `${(i / 24) * 100}%` }}></div>
          ))}
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i} className="h-px w-full bg-white opacity-10" style={{ top: `${(i / 12) * 100}%` }}></div>
          ))}
        </div>
      </div> */}

      <div className="w-full text-center mb-12 relative z-10 mt-16 sm:mt-12 md:mt-0">
        <motion.h1
          className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-6 md:mb-8 font-title"
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.1, ease: "easeOut" }}
        >
          <span className="text-jade-purple-dark">{t('hero_title_part1')}</span>
          <br />
          {t('hero_title_part2')}
        </motion.h1>
        <motion.p
          className="text-sm sm:text-lg md:text-xl mb-10 font-body opacity-80 max-w-3xl mx-auto"
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
        >
          {t('hero_subtitle')}
        </motion.p>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.3, ease: "easeOut" }}
        >
          {user ? (
            <Link href="/dashboard">
              <GlassButton className="text-lg" variant="filled">
                {t('go_to_dashboard')}
              </GlassButton>
            </Link>
          ) : (
            <Link href="/register">
              <GlassButton className="text-lg" variant="filled">
                {t('try_free')}
              </GlassButton>
            </Link>
          )}
        </motion.div>
      </div>

      <motion.div
        className="w-full max-w-2xl mx-auto relative z-10"
        initial={{ opacity: 0, y: 30 }}
        animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
        transition={{ duration: 0.6, delay: 0.4, ease: "easeOut" }}
      >
        <TerminalAnimation />
      </motion.div>
    </section>
  )
}
