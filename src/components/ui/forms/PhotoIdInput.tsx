'use client'

import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'

interface PhotoIdInputProps {
  photoId: string
  onPhotoIdChange: (event: React.ChangeEvent<HTMLInputElement>) => void
  onPhotoIdBlur?: () => void
  isCheckingPhotoId?: boolean
  photoIdExists?: boolean
  photoIdValidationMessage?: string
  readOnly?: boolean
  label?: string
  placeholder?: string
  className?: string
}

export default function PhotoIdInput({
  photoId,
  onPhotoIdChange,
  onPhotoIdBlur,
  isCheckingPhotoId = false,
  photoIdExists = false,
  photoIdValidationMessage,
  readOnly = false,
  label,
  placeholder,
  className = ""
}: PhotoIdInputProps) {
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()

  const inputId = readOnly ? 'updatePhotoId' : 'photoId'
  const defaultLabel = readOnly ? `${t('photo_id')} (${t('photo_id_title')})` : t('photo_id')
  const defaultPlaceholder = t('enter_photo_id')

  return (
    <div className={className}>
      <label htmlFor={inputId} className="block text-xs text-zinc-400 mb-1">
        {label || defaultLabel}
      </label>
      <div className="relative">
        <input
          type="text"
          id={inputId}
          value={photoId}
          onChange={onPhotoIdChange}
          onBlur={onPhotoIdBlur}
          readOnly={readOnly}
          className={`w-full ${themeConfig.secondCard} border ${themeConfig.text} rounded-lg px-3 py-2 text-base focus:outline-none focus:ring-1 ${
            readOnly ? 'cursor-not-allowed opacity-70 border-zinc-300' :
            photoIdExists ? 'border-red-500 focus:ring-red-500 focus:border-red-500' :
            photoId.trim() && !isCheckingPhotoId && !photoIdExists ? 'border-green-500 focus:ring-green-500 focus:border-green-500' :
            `${themeConfig.border} focus:ring-jade-purple focus:border-jade-purple`
          }`}
          placeholder={placeholder || defaultPlaceholder}
          required
        />

        {/* Validation Icon - Only show for non-readonly inputs */}
        {!readOnly && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            {isCheckingPhotoId ? (
              <div className="w-4 h-4 border-2 border-zinc-400 border-t-transparent rounded-full animate-spin"></div>
            ) : photoId.trim() && photoIdExists ? (
              <svg className="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            ) : photoId.trim() && !photoIdExists ? (
              <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            ) : null}
          </div>
        )}
      </div>

      {/* Validation Message - Only show for non-readonly inputs */}
      {!readOnly && photoIdValidationMessage && (
        <p className={`text-[10px] sm:text-xs mt-1 ${photoIdExists ? 'text-red-500' : 'text-green-500'}`}>
          {photoIdValidationMessage}
        </p>
      )}
    </div>
  )
}