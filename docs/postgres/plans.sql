CREATE TABLE public.plans (
  id INTEGER GENERATED BY DEFAULT AS IDENTITY NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  name TEXT NOT NULL,
  price INTEGER NOT NULL,
  total_faqs INTEGER NOT NULL,
  total_photos INTEGER NOT NULL,
  connections INTEGER NOT NULL,
  conv INTEGER NOT NULL,
  
  CONSTRAINT plans_pkey PRIMARY KEY (id),
  CONSTRAINT plans_name_unique UNIQUE (name),
  CONSTRAINT positive_price CHECK (price >= 0),
  CONSTRAINT positive_total_faqs CHECK (total_faqs > 0),
  CONSTRAINT positive_total_photos CHECK (total_photos > 0),
  CONSTRAINT positive_connections CHECK (connections > 0),
  CONSTRAINT positive_conv CHECK (conv > 0)
);