# Caching Strategy

This document outlines the comprehensive caching strategy used in the chhlat-bot project.

## Overview

The project uses a sophisticated 3-tier caching system:

1. **Server Cache** (30-minute TTL) - In-memory cache for database queries
2. **Hook Cache** - Request deduplication and local state management
3. **Component State** - Instant UI updates with optimistic rendering

## ServerCache Implementation

### Basic Configuration
- **Location**: `/src/lib/cache.ts`
- **Type**: In-memory Map with TTL (Time To Live) expiration
- **TTL**: 30 minutes for both cache types
- **Cache Keys**: 
  - `dashboard_${authId}` - Client info and usage data
  - `knowledge_${authId}` - Knowledge stats and photos

### Usage Pattern
```typescript
// Standard caching pattern
const cacheKey = `knowledge_${authId}`
const cachedData = serverCache.get(cacheKey)

if (cachedData) {
  // Cache hit - return cached data
  return cachedData
} else {
  // Cache miss - fetch from PostgreSQL directly
  const freshData = await queryOne(sql, [authId])
  serverCache.set(cacheKey, freshData, 30) // 30 min TTL
  return freshData
}
```

## Cache Types

### Dashboard Cache (`dashboard_${authId}`)
Contains essential user dashboard information:
- Client info (username, sector, plan type, language)
- Usage data (used/limit)
- **API**: `/api/user/data?cache=dashboard`
- **Database**: Direct PostgreSQL query for client table
- **Note**: `client_id` obtained from `verifyAuth()` for optimal performance

### Knowledge Cache (`knowledge_${authId}`)
Contains detailed knowledge management data:
- Client language and sector information
- Detailed knowledge stats (FAQ/photo counts and limits)
- Full photos array with metadata (JSON aggregation)
- **API**: `/api/user/data?cache=knowledge`
- **Database**: Complex PostgreSQL JOIN with JSON aggregation for photos
- **Note**: `client_id` obtained from `verifyAuth()` for optimal performance

## Authentication Integration

### Client ID Optimization
The caching system has been optimized to work with the enhanced `verifyAuth()` function:

```typescript
// Old pattern (redundant)
const clientId = cachedData?.client_id || await fetchClientIdFromDatabase()

// New optimized pattern
const { authenticated, authId, clientId } = await verifyAuth()
// clientId is now directly available without cache/database fallback
```

### API Route Pattern
```typescript
// Optimized API route using PostgreSQL-first approach
export async function GET() {
  try {
    // Get clientId directly from auth (no cache/database fallback needed)
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId || !clientId) {
      return unauthorizedResponse()
    }
    
    // Check cache first
    const cacheKey = `knowledge_${authId}`
    const cachedData = serverCache.get(cacheKey)
    if (cachedData) {
      return NextResponse.json({
        success: true,
        body: cachedData,
        cached: true
      })
    }
    
    // Cache miss - query PostgreSQL directly
    const result = await queryOne(
      `SELECT c.sector, c.lang as client_lang,
              COUNT(f.id) as faq_count,
              COALESCE(JSON_AGG(p.*) FILTER (WHERE p.id IS NOT NULL), '[]') as photos
       FROM clients c
       LEFT JOIN faqs f ON f.client_id = c.client_id AND f.is_visible = true
       LEFT JOIN photos p ON p.client_id = c.client_id
       WHERE c.auth_id = $1
       GROUP BY c.client_id`,
      [authId]
    )
    
    // Cache the result
    serverCache.set(cacheKey, result, 30)
    
    return NextResponse.json({
      success: true,
      body: result,
      cached: false
    })
  } catch (error) {
    return errorResponse(error)
  }
}
```

## Cache Management

### Manual Cache Clearing
```typescript
// Clear all server cache
POST /api/auth/clear-cache

// Response
{ success: true, message: 'Server cache cleared successfully' }
```

### Selective Cache Updates
```typescript
// Only update existing cache entries (no auto-population)
const cacheKey = `knowledge_${authId}`
const knowledgeData = serverCache.get(cacheKey)

if (knowledgeData) {
  // Update photos array and stats
  knowledgeData.photos = updatedPhotos
  knowledgeData.knowledgeStats.photoCount = newCount
  knowledgeData.knowledgeStats.photoUsagePercentage = Math.round((newCount / photoLimit) * 100)
  
  // Re-cache with fresh TTL
  serverCache.set(cacheKey, knowledgeData, 30)
}
```

### Cache Consistency Strategy
```typescript
// Pattern: PostgreSQL first, then cache update
// 1. Perform PostgreSQL transaction
// 2. Update cache only if operation succeeds
// 3. Update local state for immediate UI feedback

// Example: FAQ batch insertion
const insertedFaqs = await transaction(async (client) => {
  const results = []
  for (const faq of faqBatch) {
    const result = await client.query(
      'INSERT INTO faqs (client_id, question, answer) VALUES ($1, $2, $3) RETURNING id',
      [clientId, faq.question, faq.answer]
    )
    results.push(result.rows[0])
  }
  return results
})

// Update cache with new FAQ count
if (insertedFaqs.length > 0) {
  const cacheKey = `knowledge_${authId}`
  const knowledgeData = serverCache.get(cacheKey)
  if (knowledgeData) {
    knowledgeData.knowledgeStats.faqCount += insertedFaqs.length
    serverCache.set(cacheKey, knowledgeData, 30)
  }
  
  // Update local state
  updateFaqCountInState(newFaqCount)
}
```

## Hook Implementation

### Request Deduplication Pattern
```typescript
// Prevents multiple concurrent API calls
let dashboardRequestPromise: Promise<any> | null = null
let knowledgeRequestPromise: Promise<any> | null = null

// Shared promise ensures single request per data type
if (dashboardRequestPromise) {
  const result = await dashboardRequestPromise
  setData(result)
  return
}

// Create single shared promise
dashboardRequestPromise = fetch('/api/user/data?cache=dashboard')
```

### Dashboard Data Hook
```typescript
// useDashboardData() with request deduplication
const { data: dashboardData, loading, error } = useDashboardData()

// Features:
// - Global promise prevents duplicate concurrent requests
// - Automatic error handling and loading states
// - Request deduplication using dashboardRequestPromise
```

## Development Notes

### Environment Behavior
- **Development Mode** (`npm run dev`): Cache clears on hot reload
- **Production Mode** (`npm run start`): Cache persists as expected
- **Database Integration**: Direct PostgreSQL queries with connection pooling
- **No Auto-Population**: Cache only populated when API routes are explicitly called
- **Transaction Support**: PostgreSQL transactions ensure data consistency

### Cache Debugging
```typescript
// Test cache behavior in different environments
npm run dev   // Cache clears on hot reload
npm run start // Cache persists (production behavior)

// Check cache status in API responses
const response = await fetch('/api/user/data?cache=dashboard')
const { cached } = await response.json()
console.log('Cache hit:', cached) // true = cache hit, false = database query
```

## Performance Optimizations

### Global Request Deduplication
```typescript
// Prevents duplicate API calls across components
let dashboardRequestPromise: Promise<any> | null = null
let knowledgeRequestPromise: Promise<any> | null = null

// Shared promise ensures single request per data type
if (dashboardRequestPromise) {
  return await dashboardRequestPromise
}
```

### Component Memoization
```typescript
// Memoized calculations prevent unnecessary re-renders
const subscriptionData = useMemo(() => {
  return dashboardData?.clientInfo ? {
    plan_type: language === 'kh' ? translatePlanType(dashboardData.clientInfo.plan_type) : dashboardData.clientInfo.plan_type,
    next_billing_date: dashboardData.clientInfo.next_billing_date
  } : { plan_type: null, next_billing_date: null }
}, [dashboardData?.clientInfo, language])

const progressWidth = useMemo(() => {
  return Math.min((usageData.usage_used / usageData.usage_limit) * 100, 100)
}, [usageData.usage_used, usageData.usage_limit])
```

## Best Practices

1. **Always check cache first** before making PostgreSQL queries
2. **Update cache only if it exists** - don't create new cache entries
3. **Use PostgreSQL transactions** for operations requiring atomicity
4. **Use local state mutations** for instant UI updates
5. **Test in production mode** to verify cache persistence
6. **Monitor cache hit rates** through API response `cached` field
7. **Use request deduplication** to prevent concurrent API calls
8. **Leverage connection pooling** for optimal database performance