import { NextResponse } from 'next/server';
import { verifyAuth } from '@/utils/auth';
import { generateWebhookToken } from '@/utils/jwt';
import { serverCache } from '@/lib/cache';

// Pre-built connection limits by plan type
const PLAN_CONNECTION_LIMITS: Record<string, number> = {
  Intern: 1,
  Assistant: 3,
  Manager: 4,
  free: 1 // Default fallback
}

export async function POST(request: Request) {
  try {
    // Verify authentication
    const { authenticated, authId, clientId } = await verifyAuth();
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized' 
      }, { status: 401 });
    }

    // Validate client ID
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 });
    }


    // Parse the request body
    const payload = await request.json();
    const { webhook_url, token, type } = payload;

    // Validate required fields
    // Note: token can be empty for Telegram Business connections
    const isTokenRequired = type !== 'telegram_biz';
    if ((isTokenRequired && !token) || !type) {
      console.error('API route received incomplete data:', { token: token ? 'Present' : 'Missing', type: type ? 'Present' : 'Missing' });
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Missing required fields for platform connection.' 
      }, { status: 400 });
    }



    // Get the secure webhook URL from environment
    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
    
    if (!webhookUrl) {
      console.warn('Secure webhook URL not configured')
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Database webhook not configured'
      }, { status: 500 })
    }

    // Generate JWT token for webhook authentication
    const jwtToken = generateWebhookToken()

    // Try to get plan_type from dashboard cache first
    const dashboardCacheKey = `dashboard_${authId}`
    const cachedDashboardData = serverCache.get(dashboardCacheKey)
    let planType = 'free' // Default fallback
    let connectionLimit = 1;
    let currentConnections = 0;
    let platformConnected = {
      facebook: false,
      instagram: false,
      telegram: false
    };
    let credentialsData: any = null;

    if (cachedDashboardData?.clientInfo?.plan_type) {
      planType = cachedDashboardData.clientInfo.plan_type
      connectionLimit = PLAN_CONNECTION_LIMITS[planType] || PLAN_CONNECTION_LIMITS.free
    } else {
    }

    try {
      // If we have plan_type from cache, only fetch credentials
      if (cachedDashboardData?.clientInfo?.plan_type) {
        // Single query for credentials only
        const credentialsResponse = await fetch(webhookUrl, {
          method: 'POST',
          headers: { 
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${jwtToken}`
          },
          body: JSON.stringify({
            operation: 'SELECT',
            sql: `SELECT fb_name, ig_name, tg_name, fb_url, ig_url, tg_url, web_url 
                  FROM client_credentials 
                  WHERE client_id = $1`,
            params: [clientId]
          })
        })

        if (!credentialsResponse.ok) {
          throw new Error(`Credentials webhook request failed: ${credentialsResponse.status} ${credentialsResponse.statusText}`)
        }

        let credentialsWebhookData = await credentialsResponse.json()
        
        // Handle N8N array wrapper
        if (Array.isArray(credentialsWebhookData) && credentialsWebhookData.length > 0) {
          credentialsWebhookData = credentialsWebhookData[0]
        }
        
        if (!credentialsWebhookData.success) {
          throw new Error(credentialsWebhookData.error_msg || 'Credentials data operation failed')
        }

        const credentialsResult = credentialsWebhookData.body || []

        if (!credentialsResult || credentialsResult.length === 0) {
          console.error('Error fetching credentials data: No credentials found');
          return NextResponse.json({
            success: false,
            body: null,
            error_msg: 'Failed to verify current connections'
          }, { status: 500 });
        }

        credentialsData = credentialsResult[0];
      } else {
        // Fallback: fetch both client plan data and credentials if no cache
        const [clientPlanResponse, credentialsResponse] = await Promise.all([
          // Get client plan data
          fetch(webhookUrl, {
            method: 'POST',
            headers: { 
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${jwtToken}`
            },
            body: JSON.stringify({
              operation: 'SELECT',
              sql: `SELECT c.plan_type, p.connections 
                    FROM clients c 
                    LEFT JOIN plans p ON p.name = c.plan_type 
                    WHERE c.client_id = $1`,
              params: [clientId]
            })
          }),
          
          // Get credentials data
          fetch(webhookUrl, {
            method: 'POST',
            headers: { 
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${jwtToken}`
            },
            body: JSON.stringify({
              operation: 'SELECT',
              sql: `SELECT fb_name, ig_name, tg_name, fb_url, ig_url, tg_url, web_url 
                    FROM client_credentials 
                    WHERE client_id = $1`,
              params: [clientId]
            })
          })
        ])

        // Check client plan response
        if (!clientPlanResponse.ok) {
          throw new Error(`Client plan webhook request failed: ${clientPlanResponse.status} ${clientPlanResponse.statusText}`)
        }

        let clientPlanWebhookData = await clientPlanResponse.json()
        
        // Handle N8N array wrapper
        if (Array.isArray(clientPlanWebhookData) && clientPlanWebhookData.length > 0) {
          clientPlanWebhookData = clientPlanWebhookData[0]
        }
        
        if (!clientPlanWebhookData.success) {
          throw new Error(clientPlanWebhookData.error_msg || 'Client plan data operation failed')
        }

        const clientPlanResult = clientPlanWebhookData.body || []

        if (!clientPlanResult || clientPlanResult.length === 0) {
          console.error('Error fetching client plan data: No client found');
          return NextResponse.json({
            success: false,
            body: null,
            error_msg: 'Failed to verify account information'
          }, { status: 500 });
        }

        const clientPlanData = clientPlanResult[0];
        planType = clientPlanData.plan_type || 'free'
        connectionLimit = clientPlanData.connections || 1;

        // Check credentials response
        if (!credentialsResponse.ok) {
          throw new Error(`Credentials webhook request failed: ${credentialsResponse.status} ${credentialsResponse.statusText}`)
        }

        let credentialsWebhookData = await credentialsResponse.json()
        
        // Handle N8N array wrapper
        if (Array.isArray(credentialsWebhookData) && credentialsWebhookData.length > 0) {
          credentialsWebhookData = credentialsWebhookData[0]
        }
        
        if (!credentialsWebhookData.success) {
          throw new Error(credentialsWebhookData.error_msg || 'Credentials data operation failed')
        }

        const credentialsResult = credentialsWebhookData.body || []

        if (!credentialsResult || credentialsResult.length === 0) {
          console.error('Error fetching credentials data: No credentials found');
          return NextResponse.json({
            success: false,
            body: null,
            error_msg: 'Failed to verify current connections'
          }, { status: 500 });
        }

        credentialsData = credentialsResult[0];
      }

      // Count current connections based on platform names (more secure)
      if (credentialsData.fb_name) currentConnections++;
      if (credentialsData.ig_name) currentConnections++;
      if (credentialsData.tg_name) currentConnections++;

      // Check if platforms are already connected
      platformConnected = {
        facebook: !!credentialsData.fb_name,
        instagram: !!credentialsData.ig_name,
        telegram: !!credentialsData.tg_name
      };

      // Determine which platform is being connected
      let platformType = type;
      if (type.includes('/')) {
        platformType = type.split('/')[0]; // Handle cases like "instagram/page_id"
      }
      if (type === 'telegram_biz') {
        platformType = 'telegram'; // Both telegram and telegram_biz use tg_name
      }

      // Only validate limit if this is a new connection
      if (!platformConnected[platformType as keyof typeof platformConnected] && currentConnections >= connectionLimit) {
        return NextResponse.json({
          success: false,
          body: null,
          error_msg: `Connection limit reached (${currentConnections}/${connectionLimit}). Please upgrade your plan to connect more platforms.`
        }, { status: 403 });
      }

    } catch (limitError) {
      console.error('Error checking connection limits:', limitError);
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Failed to verify connection limits'
      }, { status: 500 });
    }


    // Get webhook URL from stored credentials (except Telegram Business which uses @username)
    let actualWebhookUrl = '';
    
    if (type === 'telegram_biz') {
      // Telegram Business uses @username from request
      actualWebhookUrl = webhook_url;
    } else {
      // All other platforms use stored webhook URLs from database
      let platformType = type;
      if (type.includes('/')) {
        platformType = type.split('/')[0]; // Handle cases like "instagram/page_id"
      }

      switch (platformType) {
        case 'facebook':
          actualWebhookUrl = credentialsData.fb_url;
          break;
        case 'instagram':
          actualWebhookUrl = credentialsData.ig_url;
          break;
        case 'whatsapp':
          actualWebhookUrl = credentialsData.wa_url;
          break;
        case 'telegram':
          actualWebhookUrl = credentialsData.tg_url;
          break;
        case 'web':
          actualWebhookUrl = credentialsData.web_url;
          break;
        default:
          actualWebhookUrl = '';
      }
    }

    // Forward the request to the secure webhook with JWT authentication
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${jwtToken}`
      },
      body: JSON.stringify({
        operation: 'connect',
        mode: 'connection',
        client_id: clientId,
        webhook_url: actualWebhookUrl,
        token,
        type
      }),
    });

    // Handle webhook response using standard N8N pattern
    if (!response.ok) {
      throw new Error(`Connection webhook request failed: ${response.status} ${response.statusText}`)
    }

    let webhookData = await response.json()
    
    // Handle N8N array wrapper
    if (Array.isArray(webhookData) && webhookData.length > 0) {
      webhookData = webhookData[0]
    }
    
    if (!webhookData.success) {
      // Preserve error code for frontend error handling
      return NextResponse.json({
        success: false,
        body: webhookData.body || null,
        error_msg: webhookData.error_msg || 'Connection operation failed',
        code: webhookData.code // Preserve error code for specific error messages
      }, { status: 400 });
    }


    // Return the webhook response directly (maintains N8N format)
    return NextResponse.json(webhookData);

  } catch (error: unknown) {
    console.error('Error in connection webhook API route:', error);
    return NextResponse.json({
      success: false,
      body: null,
      error_msg: error instanceof Error ? error.message : 'Failed to process connection request.'
    }, { status: 500 });
  }
}