'use client'

import { ReactNode, useRef } from 'react'
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'
import { Button } from '@/components/ui'
import WebhookCopyButton from './WebhookCopyButton'

interface PlatformConnectionFormProps {
  platform: string
  name: string
  icon: ReactNode
  description: string
  isConnected: boolean
  connectingPlatform: string | null
  onConnect: (platform: string) => void
  webhookStates: { [key: string]: { isCopied: boolean } }
  onCopyWebhook: (id: string) => Promise<void>
  onCopyBotName?: () => Promise<void>
  
  // Platform-specific props
  telegramConnectionType?: 'bot' | 'business'
  setTelegramConnectionType?: (type: 'bot' | 'business') => void
  
  // Refs for input values
  tokenRef?: React.RefObject<HTMLInputElement>
  secondaryRef?: React.RefObject<HTMLInputElement> // For Instagram ID, etc.
  onTokenChange?: (platform: string, value: string) => void
}

export default function PlatformConnectionForm({
  platform,
  name,
  icon,
  description,
  isConnected,
  connectingPlatform,
  onConnect,
  webhookStates,
  onCopyWebhook,
  onCopyBotName,
  telegramConnectionType = 'business',
  setTelegramConnectionType,
  tokenRef,
  secondaryRef,
  onTokenChange
}: PlatformConnectionFormProps) {
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()

  const renderWebhooks = () => {
    if (platform === 'facebook') {
      return (
        <div className="flex flex-col sm:flex-row gap-3">
          <div className="relative w-full sm:w-[40%]">
            <WebhookCopyButton
              id="privacy-policy"
              displayText="https://www.chhlatbot.com/privacy"
              title="Click to copy Privacy Policy URL"
              onCopy={onCopyWebhook}
              isCopied={webhookStates['privacy-policy']?.isCopied || false}
            />
          </div>
          <div className="relative flex-1">
            <WebhookCopyButton
              id="fb-webhook"
              displayText="••••••••••••••••••"
              title="Click to copy webhook URL"
              onCopy={onCopyWebhook}
              isCopied={webhookStates['fb-webhook']?.isCopied || false}
            />
          </div>
        </div>
      )
    }

    if (platform === 'instagram') {
      return (
        <div className="relative">
          <WebhookCopyButton
            id="ig-webhook"
            displayText="••••••••••••••••••"
            title="Click to copy webhook URL"
            onCopy={onCopyWebhook}
            isCopied={webhookStates['ig-webhook']?.isCopied || false}
            className="flex-1"
          />
        </div>
      )
    }

    return null
  }

  const renderConnectionInputs = () => {
    if (isConnected) {
      return (
        <>
          <div className={`flex-1 ${themeConfig.secondCard} border ${themeConfig.border} rounded-lg px-4 py-2.5 text-sm ${themeConfig.textSecondary} font-body`}>
            ••••••••••••••••••••••••••••••••••••••••••••••••••••
          </div>
          <button
            className="bg-jade-purple text-white rounded-lg px-5 py-2.5 text-sm font-medium transition-colors flex items-center justify-center font-body"
            disabled
          >
            {t('connected')}
          </button>
        </>
      )
    }

    if (platform === 'instagram') {
      return (
        <>
          <div className="relative w-full sm:w-[40%]">
            <input
              ref={secondaryRef}
              type="password"
              placeholder={t('enter_id')}
              defaultValue=""
              className={`w-full px-3 py-2 ${themeConfig.secondCard} border ${themeConfig.border} rounded-lg ${themeConfig.text} font-body focus:outline-none ${themeConfig.borderActive}`}
              style={{ fontSize: '16px' }}
            />
          </div>
          <div className="flex flex-col gap-2 flex-1">
            <div className="relative">
              <input
                ref={tokenRef}
                type="password"
                placeholder={t('enter_access_token')}
                defaultValue=""
                className={`w-full px-3 py-2 ${themeConfig.secondCard} border ${themeConfig.border} rounded-lg ${themeConfig.text} font-body focus:outline-none ${themeConfig.borderActive}`}
                style={{ fontSize: '16px' }}
                  onChange={(e) => onTokenChange?.(platform, e.target.value)}
              />
            </div>
          </div>
          <Button
            variant="primary"
            size="sm"
            onClick={() => onConnect(platform)}
            className="px-5 py-2.5 md:px-8 md:min-w-[120px]"
          >
            {t('connect')}
          </Button>
        </>
      )
    }

    if (platform === 'telegram') {
      if (telegramConnectionType === 'business') {
        return (
          <>
            <div className="relative w-full sm:w-[40%]">
              <WebhookCopyButton
                id="telegram-bot-name"
                displayText="yourchhlatbot"
                title="Click to copy bot name"
                onCopy={onCopyBotName || onCopyWebhook}
                isCopied={webhookStates['telegram-bot-name']?.isCopied || false}
              />
            </div>
            <div className="flex flex-col gap-2 flex-1">
              <div className="relative">
                <input
                  ref={tokenRef}
                  type="text"
                  placeholder={t('telegram_username_placeholder')}
                  defaultValue=""
                  className={`w-full px-3 py-2 ${themeConfig.secondCard} border ${themeConfig.border} rounded-lg ${themeConfig.text} font-body focus:outline-none ${themeConfig.borderActive}`}
                  style={{ fontSize: '16px' }}
                  onChange={(e) => {
                    if (!e.target.value.startsWith('@') && e.target.value.length > 0) {
                      e.target.value = '@' + e.target.value
                    }
                  }}
                />
              </div>
            </div>
            <Button
              variant="primary"
              size="sm"
              onClick={() => onConnect(platform)}
              className="px-5 py-2.5 md:px-8 md:min-w-[120px]"
            >
              {t('connect')}
            </Button>
          </>
        )
      } else {
        return (
          <>
            <div className="relative flex-1">
              <input
                ref={tokenRef}
                type="password"
                placeholder={t('enter_telegram_bot_token')}
                defaultValue=""
                className={`w-full px-3 py-2 ${themeConfig.secondCard} border ${themeConfig.border} rounded-lg ${themeConfig.text} font-body focus:outline-none ${themeConfig.borderActive}`}
                style={{ fontSize: '16px' }}
                  onChange={(e) => onTokenChange?.(platform, e.target.value)}
              />
            </div>
            <Button
              variant="primary"
              size="sm"
              onClick={() => onConnect(platform)}
              className="px-5 py-2.5 md:px-8 md:min-w-[120px]"
            >
              {t('connect')}
            </Button>
          </>
        )
      }
    }

    if (platform === 'web') {
      return (
        <div className="flex flex-col items-center">
          <p className={`${themeConfig.textMuted} mt-2 font-body`}>{t('web_api_coming_soon')}</p>
        </div>
      )
    }

    // Default case for Facebook and other simple platforms
    return (
      <>
        <div className="relative flex-1">
          <input
            ref={tokenRef}
            type="password"
            placeholder={t('enter_access_token')}
            defaultValue=""
            className={`w-full px-3 py-2 ${themeConfig.secondCard} border ${themeConfig.border} rounded-lg ${themeConfig.text} font-body focus:outline-none ${themeConfig.borderActive} ${themeConfig.borderHover}`}
            style={{ fontSize: '16px' }}
            onChange={(e) => onTokenChange?.(platform, e.target.value)}
          />
        </div>
        <Button
          variant="primary"
          size="sm"
          onClick={() => onConnect(platform)}
          className="px-5 py-2.5 md:px-8 md:min-w-[120px]"
        >
          {t('connect')}
        </Button>
      </>
    )
  }

  const renderTelegramTabs = () => {
    if (platform !== 'telegram' || !setTelegramConnectionType) return null

    return (
      <div className="grid grid-cols-2 gap-2 mb-3">
        <button
          onClick={() => setTelegramConnectionType('business')}
          className={`relative group p-3 rounded-xl border transition-all duration-300 overflow-hidden ${
            telegramConnectionType === 'business'
              ? `${themeConfig.secondCard} ${themeConfig.border} ${themeConfig.text}`
              : `${themeConfig.card} ${themeConfig.border} ${themeConfig.textSecondary} ${
                  theme === 'light' ? '' : 'hover:text-white hover:bg-black/30'
                }`
          }`}
        >
          <div className="relative z-10 text-center">
            <h4 className="text-xs sm:text-base font-title">{t('telegram_business')}</h4>
          </div>
        </button>

        <button
          onClick={() => setTelegramConnectionType('bot')}
          className={`relative group p-3 rounded-xl border transition-all duration-300 overflow-hidden ${
            telegramConnectionType === 'bot'
              ? `${themeConfig.secondCard} ${themeConfig.border} ${themeConfig.text}`
              : `${themeConfig.card} ${themeConfig.border} ${themeConfig.textSecondary} ${
                  theme === 'light' ? '' : 'hover:text-white hover:bg-black/30'
                }`
          }`}
        >
          <div className="relative z-10 text-center">
            <h4 className="text-xs sm:text-base font-title">{t('telegram_bot')}</h4>
          </div>
        </button>
      </div>
    )
  }

  return (
    <div className={`${themeConfig.card} border ${themeConfig.border} rounded-xl p-6 transition-all`}>
      <div className="flex items-center mb-4">
        <div className={`w-10 h-10 ${themeConfig.secondCard} rounded-full flex items-center justify-center mr-4 border ${themeConfig.border}`} style={{ minWidth: '2.5rem' }}>
          {icon}
        </div>
        <div style={{ flex: 1 }}>
          <h3 className={`font-medium ${themeConfig.text} text-lg font-title`}>{name}</h3>
          <p className={`${themeConfig.textSecondary} text-sm font-body`}>{description}</p>
        </div>
      </div>

      <div className="space-y-3">
        {renderWebhooks()}
        {renderTelegramTabs()}
        <div className="flex flex-col sm:flex-row gap-3">
          {renderConnectionInputs()}
        </div>
      </div>
    </div>
  )
}