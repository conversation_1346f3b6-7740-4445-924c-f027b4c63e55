# Photo Management System

Handles photo upload, optimization, and delivery with client-side compression and Cloudflare Images integration.

## System Overview

### Core Features
- **CRUD Operations**: Upload, update, and delete with dependency checking
- **Client-side Compression**: 700KB max, 1080px dimension, JPEG format
- **Cloudflare Images**: Cost-optimized delivery with multiple configurations
- **Real-time Features**: Validation, search, and instant UI updates
- **Gallery Interface**: Modal with keyboard navigation and lazy loading
- **Drag & Drop**: Multi-file upload with validation

### Architecture
- **Data Source**: Knowledge cache via `useKnowledgeData()` hook
- **Storage**: Cloudflare R2 with user-scoped directory structure
- **Delivery**: Dual approach - Worker URL + direct Cloudflare Images
- **State Management**: Optimistic updates with local state mutations

## Photo Operations

### Upload Flow
1. **Client Compression**: 700KB max, 1080px dimension, JPEG format
2. **R2 Upload**: Via `/api/file/photos` with user-scoped paths
3. **Metadata Save**: Via `/api/knowledge/photos` with photo details
4. **State Update**: Instant UI update with `addPhotoToState()`

### Update Flow
1. **File Management**: Keep existing + upload new files to R2
2. **Cleanup**: Delete removed files from R2 storage
3. **Database Update**: Via `/api/knowledge/photos` PUT endpoint
4. **State Update**: Instant UI update with `updatePhotoInState()`

### Delete Flow
1. **Dependency Check**: Verify no FAQs are linked to photo
2. **User Confirmation**: Show cannot-delete modal if dependencies exist
3. **Complete Deletion**: Database + R2 cleanup
4. **State Update**: Instant UI update with `removePhotoFromState()`

## Client-Side Processing

### Image Compression
- **Size Limit**: 700KB maximum with 1080px dimension
- **Format**: Always convert to JPEG for consistency
- **Quality**: 80% initial quality with aspect ratio preservation
- **Performance**: Uses Web Workers for non-blocking compression

### File Validation
- **Supported Types**: JPEG, JPG, PNG
- **Size Limit**: 5MB maximum before compression
- **Real-time Validation**: Instant feedback on file selection
- **Error Handling**: Clear error messages for invalid files

## Cloudflare Images Integration

### Dual Optimization Approach
- **Primary**: Cloudflare Worker URL (most reliable)
- **Fallback**: Direct Cloudflare Images API
- **No Optimization**: Return original URL if neither available

### Optimization Tiers
- **Gallery Images**: 1200x900px, 90% quality for high-quality viewing
- **Photo List**: 300x300px, 80% quality for medium thumbnails
- **Small Thumbnails**: Original URLs for cost efficiency (≤100px)
- **Responsive**: Mobile (400x300), Tablet (800x600), Desktop (1200x900)

### Cost Optimization Strategy
- **Skip Small Images**: No optimization for images ≤200px dimensions
- **Selective Optimization**: Only optimize when size/quality benefit exists
- **Configuration-based**: Different settings for different use cases

## User Interface Features

### Photo ID Validation
- **Real-time Validation**: 500ms debounced checking
- **Duplicate Detection**: Check existing photo IDs
- **Visual Feedback**: Loading states and validation indicators

### Search Functionality
- **Local Search**: Real-time filtering of cached photos
- **API Fallback**: Search via `/api/knowledge/search-photos` if cache miss
- **Performance**: Client-side filtering for instant results

### Pagination
- **Configurable**: Adjustable items per page
- **Client-side**: Slice filtered results for pagination
- **Navigation**: Standard pagination controls

### Gallery Modal
- **Multi-image Preview**: Support for multiple images per photo
- **Keyboard Navigation**: Arrow keys for navigation, Escape to close
- **Image Preloading**: Smooth navigation with preloaded images

### Drag & Drop Upload
- **Multi-file Support**: Up to 4 files per upload
- **File Validation**: Instant validation on drop
- **Visual Feedback**: Clear error messages and upload progress

## Performance Strategy

### Optimization Techniques
- **Lazy Loading**: Images load only when in viewport
- **Thumbnail Component**: Optimized with loading states and error handling
- **Image Preloading**: Gallery images preloaded for smooth navigation
- **Client-side Compression**: Reduce upload size and processing time

### Caching Layers
1. **Server Cache**: 30-minute TTL for database queries
2. **Hook Cache**: Request deduplication and local state
3. **Component State**: Instant UI updates with optimistic rendering

### UI Performance
- **Optimistic Updates**: Immediate UI feedback for all operations
- **Debounced Validation**: Reduce API calls with 500ms debouncing
- **Pagination**: Limit rendered items for better performance

## API Endpoints

### POST `/api/file/photos` - R2 File Operations
**Purpose**: Upload multiple files to Cloudflare R2
**Features**:
- Client-side compression (700KB max, 1080px)
- User-scoped storage (`photos/${authId}/`)
- Batch operations with arrays
- Security validation

### CRUD `/api/knowledge/photos` - Photo Operations
**Purpose**: Complete photo lifecycle management
**Features**:
- POST: Create with R2 upload integration
- PUT: Update with file path management
- DELETE: Dependency checking and R2 cleanup
- Real-time cache updates

### POST `/api/knowledge/search-photos` - Photo Search
**Purpose**: Search photos by photo_id
**Features**:
- PostgreSQL ILIKE search
- 20 result limit for performance
- User-scoped results
- Metadata with URLs

### Common Patterns
- **User Authentication**: All endpoints require `verifyAuth()`
- **File Path Security**: Validate user-owned paths only
- **Batch Operations**: Support multiple files per request
- **Error Handling**: Comprehensive validation and error messages

## Implementation Status

### Core Features ✅
- Upload, update, delete operations with dependency checking
- Client-side compression (700KB max, 1080px, JPEG)
- Cloudflare Images integration with cost optimization
- Real-time validation and search functionality
- Gallery modal with keyboard navigation
- Drag & drop interface with multi-file support

### Security & Validation
- **File Path Security**: User-scoped paths (`photos/${authId}/`)
- **File Type Validation**: JPEG, JPG, PNG only
- **Size Limits**: 5MB max before compression, 700KB after
- **Authentication**: All endpoints require `verifyAuth()`

### Cost Optimization
- **Selective Optimization**: Skip small images (≤200px)
- **Tiered Quality**: 90% gallery, 80% lists, original thumbnails
- **Configuration-based**: Different settings per use case

### Integration Points
- **Storage**: Cloudflare R2 with user-scoped directories
- **Delivery**: Dual approach (Worker URL + direct API)
- **Caching**: 3-tier strategy with optimistic updates
- **Database**: PostgreSQL with real-time cache updates

### Key Patterns
1. Always compress images client-side before upload
2. Use local state mutations for instant UI feedback
3. Implement proper error handling for file operations
4. Validate file paths for security
5. Use lazy loading for image thumbnails
6. Monitor Cloudflare Images usage for cost optimization