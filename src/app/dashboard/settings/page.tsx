'use client'

import { useState, useEffect } from 'react'
import { createClientComponentClient } from '@/utils/supabase/client'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
// Removed clientInfo imports - now using dashboard cache as single source of truth
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'
// Client cache clearing removed - only server-side cache needs clearing
import { Button, LinkButton } from '@/components/ui'
import DashboardHeader from '@/components/ui/DashboardHeader'
import DeleteConfirmationModal from '@/components/ui/modals/DeleteConfirmationModal'
import UpdateStatusOverlay from '@/components/ui/knowledge/UpdateStatusOverlay'

export default function SettingsPage() {
  const [oldPassword, setOldPassword] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [error, setError] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isSigningOut, setIsSigningOut] = useState(false)
  // const [clientInfo, setClientInfoState] = useState<ClientInfo | null>(null)
  // Language change functionality temporarily disabled
  // const [currentLang, setCurrentLang] = useState<string>('en')
  // const [showLangConfirm, setShowLangConfirm] = useState(false)
  // const [showSecondConfirm, setShowSecondConfirm] = useState(false)
  // const [newLang, setNewLang] = useState<string>('en')
  // const [showLangDropdown, setShowLangDropdown] = useState(false)
  // const [isChangingLang, setIsChangingLang] = useState(false)
  const [showLogoutConfirmation, setShowLogoutConfirmation] = useState(false)
  const [showSuccessPopup, setShowSuccessPopup] = useState(false)
  // const [showLangSuccessPopup, setShowLangSuccessPopup] = useState(false)

  const router = useRouter()
  const supabase = createClientComponentClient()
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()

  // Disable page scroll when any popup is open
  useEffect(() => {
    if (showLogoutConfirmation || showSuccessPopup) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = '';
      };
    }
  }, [showLogoutConfirmation, showSuccessPopup])

  // Language options temporarily disabled
  // const languageOptions = [
  //   { code: 'km', name: 'Khmer', nativeName: 'ខ្មែរ' },
  //   { code: 'en', name: 'English', nativeName: 'English' },
  //   { code: 'zh', name: 'Chinese', nativeName: '中文' },
  //   { code: 'th', name: 'Thai', nativeName: 'ไทย' },
  //   { code: 'vi', name: 'Vietnamese', nativeName: 'Tiếng Việt' },
  //   { code: 'ko', name: 'Korean', nativeName: '한국어' },
  //   { code: 'id', name: 'Indonesian', nativeName: 'Bahasa Indonesia' },
  //   { code: 'fr', name: 'French', nativeName: 'Français' },
  //   { code: 'ja', name: 'Japanese', nativeName: '日本語' }
  // ]

  // Load client info on component mount - temporarily disabled
  // useEffect(() => {
  //   const info = getClientInfo()
  //   setClientInfoState(info)
  //   // Language functionality temporarily disabled
  //   // if (info?.lang) {
  //   //   // Handle legacy 'kh' to 'km' conversion
  //   //   const lang = info.lang === 'kh' ? 'km' : info.lang
  //   //   setCurrentLang(lang)
  //   // }
  // }, [])

  // Close dropdown when clicking outside - temporarily disabled
  // useEffect(() => {
  //   const handleClickOutside = (event: MouseEvent) => {
  //     const target = event.target as Element
  //     if (showLangDropdown && !target.closest('.language-settings-section')) {
  //       setShowLangDropdown(false)
  //     }
  //   }

  //   if (showLangDropdown) {
  //     document.addEventListener('mousedown', handleClickOutside)
  //   }

  //   return () => {
  //     document.removeEventListener('mousedown', handleClickOutside)
  //   }
  // }, [showLangDropdown])

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setIsLoading(true)

    if (newPassword !== confirmPassword) {
      setError(t('settings_passwords_no_match'))
      setIsLoading(false)
      return
    }

    try {
      // Get the current user's email first
      const { data: userData } = await supabase.auth.getUser()
      const userEmail = userData?.user?.email

      if (!userEmail) {
        throw new Error('Unable to verify user email')
      }

      // First sign in with the old password to verify it
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: userEmail,
        password: oldPassword
      })

      if (signInError) {
        throw new Error(t('settings_current_password_incorrect'))
      }

      // Then update to the new password
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      })

      if (error) throw error

      // Show success popup for 1 second
      setShowSuccessPopup(true)
      setTimeout(() => {
        setShowSuccessPopup(false)
      }, 1500)

      // Clear form fields
      setOldPassword('')
      setNewPassword('')
      setConfirmPassword('')
    } catch (error: any) {
      setError(error.message || t('settings_password_update_failed'))
    } finally {
      setIsLoading(false)
    }
  }

  const handleSignOut = async () => {
    try {
      setIsSigningOut(true)

      // Clear server-side cache
      try {
        await fetch('/api/auth/clear-cache', {
          method: 'POST',
          cache: 'no-store'
        })
      } catch (cacheError) {
        console.warn('Failed to clear server cache:', cacheError)
        // Continue with logout even if cache clearing fails
      }

      const { error } = await supabase.auth.signOut()
      if (error) throw error

      await fetch('/api/auth/signout', {
        method: 'POST',
        cache: 'no-store'
      })

      router.push('/access')

      setTimeout(() => {
        router.refresh()
      }, 100)
    } catch (error) {
      console.error('Error signing out:', error)
      router.push('/')
    }
  }

  const confirmSignOut = () => {
    setShowLogoutConfirmation(true)
  }

  // Language change functions temporarily disabled
  // const handleLanguageChange = async (lang: string) => {
  //   setNewLang(lang)
  //   setShowLangConfirm(true)
  //   setShowLangDropdown(false)
  // }

  // const handleFirstConfirm = () => {
  //   setShowLangConfirm(false)
  //   setShowSecondConfirm(true)
  // }

  // const handleSecondConfirm = async () => {
  //   if (!clientInfo) return

  //   try {
  //     setIsChangingLang(true)

  //     // Prepare update objects for both tables
  //     let clientsUpdate: any = { lang: newLang }
  //     let configsUpdate: any = { lang: newLang }

  //     // Special case for English: set lang_2 to 'km' (Khmer)
  //     if (newLang === 'en') {
  //       clientsUpdate.lang_2 = 'km'
  //       configsUpdate.lang_2 = 'km'
  //     }
  //     // For other languages, don't touch lang_2 field

  //     // Create a new client info object with the updated language
  //     const updatedClientInfo: ClientInfo = {
  //       ...clientInfo,
  //       lang: newLang
  //     }

  //     // Update the client info in session storage
  //     setClientInfo(updatedClientInfo)

  //     // Update the state
  //     setClientInfoState(updatedClientInfo)
  //     setCurrentLang(newLang)

  //     // Update the language in the clients table
  //     const { error: clientsError } = await supabase
  //       .from('clients')
  //       .update(clientsUpdate)
  //       .eq('client_id', clientInfo.client_id)

  //     if (clientsError) {
  //       console.error('Error updating language in clients table:', clientsError)
  //       setError('Failed to update language setting. Please try again.')
  //       return
  //     }

  //     // Update the language in the configs table
  //     const { error: configsError } = await supabase
  //       .from('configs')
  //       .update(configsUpdate)
  //       .eq('client_id', clientInfo.client_id)

  //     if (configsError) {
  //       console.error('Error updating language in configs table:', configsError)
  //       setError('Failed to update language setting in configs. Please try again.')
  //     } else {
  //       // Show language success popup for 1 second
  //       setShowLangSuccessPopup(true)
  //       setTimeout(() => {
  //         setShowLangSuccessPopup(false)
  //       }, 1000)
  //     }
  //   } catch (error) {
  //     console.error('Error changing language:', error)
  //     setError('Failed to update language setting. Please try again.')
  //   } finally {
  //     setIsChangingLang(false)
  //     setShowSecondConfirm(false)
  //   }
  // }

  return (
    <div className={`min-h-screen ${themeConfig.pageBackground} flex flex-col relative pb-16`}>
      {/* Conditional background effects based on theme */}
      {themeConfig.backgroundEffects}
      {theme === 'dark' && (
        <>
          <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-white/5 rounded-full blur-[150px] -z-10"></div>
          <div className="absolute bottom-1/3 right-1/3 w-1/3 h-1/3 bg-jade-purple/5 rounded-full blur-[120px] -z-10"></div>
        </>
      )}

      {/* Modern Glassmorphism Header */}
      <header className="relative">
        <div className="container mx-auto px-3 py-3">
          <div
            className={`relative ${themeConfig.card} rounded-2xl px-4 py-3 border ${themeConfig.border} transition-all duration-300 overflow-hidden`}
            style={theme === 'dark' ? {
              boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
            } : {
              boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
            }}
          >

            {/* Content */}
            <div className="relative z-10 flex justify-center items-center">
              <LinkButton 
                href="/dashboard" 
                variant="ghost" 
                className="p-0 hover:bg-transparent active:scale-95"
              >
                <img
                  src={themeConfig.logo}
                  alt="Chhlat Bot"
                  className="h-8 w-auto transition-transform duration-300 hover:scale-105"
                />
              </LinkButton>
            </div>
          </div>
        </div>
      </header>

      <div className="flex-grow container mx-auto px-4 py-8">
        <div>
          <DashboardHeader backHref="/dashboard" titleKey="settings" />

          <div className="max-w-md mx-auto">
            {/* Language Settings Section - Temporarily Disabled */}
            {/* <div
              className="relative bg-gradient-to-br from-white/[0.08] to-white/[0.02] rounded-2xl p-6 mb-8 border border-white/20 hover:border-white/40 transition-all duration-300 group language-settings-section"
              style={{
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(255, 255, 255, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
                zIndex: showLangDropdown ? 1000 : 'auto'
              }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-jade-purple/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"></div>
              <div className="relative z-10">
              <h2 className="text-xl font-bold mb-4 font-title text-white">{t('settings_language_title')}</h2>
              <p className="text-zinc-400 text-sm mb-4 font-body">
                {t('settings_language_description')}
              </p>
              <p className="text-zinc-400 text-sm mb-6 font-body">
                {t('settings_language_explanation')}
                <br /><br />
                {t('settings_language_english_note')}
              </p>

              <div className="relative z-10">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center justify-center w-10 h-10 rounded-full bg-jade-purple text-white">
                      <FaGlobe size={18} />
                    </div>
                    <div>
                      <div className="text-white font-medium font-body">
                        {languageOptions.find(lang => lang.code === currentLang)?.name || 'Unknown'}
                      </div>
                      <div className="text-zinc-400 text-sm font-body">
                        {languageOptions.find(lang => lang.code === currentLang)?.nativeName || ''}
                      </div>
                    </div>
                  </div>
                  <span className="px-3 py-1 bg-jade-purple text-white text-sm rounded-lg font-body">
                    {t('settings_language_active')}
                  </span>
                </div>

                <div className="relative z-20">
                  <button
                    onClick={() => setShowLangDropdown(!showLangDropdown)}
                    disabled={isChangingLang}
                    className="w-full flex items-center justify-between px-4 py-3 bg-black/30 border border-white/20 rounded-lg text-white font-body hover:bg-black/40 hover:border-white/30 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    style={{
                      boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.05)'
                    }}
                  >
                    <span>{t('settings_change_language')}</span>
                    <svg
                      className={`w-5 h-5 transition-transform duration-200 ${showLangDropdown ? 'rotate-180' : ''}`}
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>

                  {showLangDropdown && (
                    <div className="absolute top-full left-0 right-0 mt-2 bg-black backdrop-blur-xs border border-white/30 rounded-lg shadow-xl max-h-64 overflow-y-auto"
                      style={{
                        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)',
                        zIndex: 99999
                      }}
                    >
                      {languageOptions.map((language) => (
                        <button
                          key={language.code}
                          onClick={(e) => {
                            e.preventDefault()
                            e.stopPropagation()
                            handleLanguageChange(language.code)
                          }}
                          disabled={currentLang === language.code || isChangingLang}
                          className={`w-full flex items-center justify-between px-4 py-3 text-left hover:bg-white/15 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed ${
                            currentLang === language.code ? 'bg-jade-purple-dark/80' : ''
                          }`}
                        >
                          <div className="flex items-center space-x-3">
                            <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                              currentLang === language.code ? 'bg-jade-purple text-white' : 'bg-white/10 text-zinc-400'
                            }`}>
                              {language.code === 'km' ? (
                                <span className="text-xs font-semibold">ខ្មែរ</span>
                              ) : (
                                <FaGlobe size={14} />
                              )}
                            </div>
                            <div>
                              <div className="text-white font-medium font-body text-sm">
                                {language.name}
                              </div>
                              <div className="text-zinc-400 text-xs font-body">
                                {language.nativeName}
                              </div>
                            </div>
                          </div>
                          {currentLang === language.code && (
                            <div className="text-jade-purple text-sm font-body">
                              ✓
                            </div>
                          )}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              </div>
            </div> */}

            <div
              className={`relative ${themeConfig.card} rounded-2xl p-6 mb-8 border ${themeConfig.border} transition-all duration-300 group overflow-hidden`}
              style={theme === 'dark' ? {
                boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
              } : {
                boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
              }}
            >
              <div className="relative z-10">
              <h2 className={`text-xl font-bold mb-6 font-title ${themeConfig.text}`}>{t('settings_change_password_title')}</h2>

            <form onSubmit={handlePasswordChange} className="space-y-4">
              <div>
                <label htmlFor="oldPassword" className={`block text-sm font-medium ${themeConfig.textSecondary} font-body mb-1`}>
                  {t('settings_current_password')}
                </label>
                <input
                  type="password"
                  id="oldPassword"
                  value={oldPassword}
                  onChange={(e) => setOldPassword(e.target.value)}
                  required
                  className={`w-full px-3 py-2 ${themeConfig.secondCard} border ${themeConfig.border} rounded-lg ${themeConfig.text} font-body focus:outline-none ${themeConfig.borderHover} ${themeConfig.borderActive}`}
                />
              </div>

              <div>
                <label htmlFor="newPassword" className={`block text-sm font-medium ${themeConfig.textSecondary} font-body mb-1`}>
                  {t('settings_new_password')}
                </label>
                <input
                  type="password"
                  id="newPassword"
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  required
                  className={`w-full px-3 py-2 ${themeConfig.secondCard} border ${themeConfig.border} rounded-lg ${themeConfig.text} font-body focus:outline-none ${themeConfig.borderHover} ${themeConfig.borderActive}`}
                />
              </div>

              <div>
                <label htmlFor="confirmPassword" className={`block text-sm font-medium ${themeConfig.textSecondary} font-body mb-1`}>
                  {t('settings_confirm_password')}
                </label>
                <input
                  type="password"
                  id="confirmPassword"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                  className={`w-full px-3 py-2 ${themeConfig.secondCard} border ${themeConfig.border} rounded-lg ${themeConfig.text} font-body focus:outline-none ${themeConfig.borderHover} ${themeConfig.borderActive}`}
                />
              </div>

              {error && (
                <div className={`${themeConfig.errorText} text-sm font-body`}>{error}</div>
              )}

              <button
                type="submit"
                disabled={isLoading}
                className="w-full py-3 px-4 bg-jade-purple-dark hover:bg-jade-purple text-white rounded-lg transition-colors font-body text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? t('settings_updating') : t('settings_update_password')}
              </button>
            </form>
              </div>
          </div>

          {/* Appearance Section */}
          {/* <div
            className="relative bg-white/[0.075] rounded-2xl p-6 mb-8 border border-white/20 hover:border-white/40 transition-all duration-300 group overflow-hidden"
          >
            <div className="relative z-10">
              <h2 className="text-xl font-bold mb-6 font-title text-white">Appearance</h2>
              
              <div className="grid grid-cols-2 gap-3">
                <label className="relative flex flex-col items-center p-4 bg-black/30 border-2 border-jade-purple/80 rounded-lg hover:bg-black/40 hover:border-jade-purple transition-all duration-200 cursor-pointer">

                  <div className="absolute top-2 right-2 w-5 h-5 bg-jade-purple rounded-full flex items-center justify-center">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="flex items-center justify-center w-12 h-12 rounded-full bg-gray-800 mb-3">
                    <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                    </svg>
                  </div>
                  <div className="text-center">
                    <div className="text-white font-medium font-body mb-1">Dark</div>
                    <div className="text-zinc-400 text-xs font-body">Default theme</div>
                  </div>
                  <input
                    type="radio"
                    name="theme"
                    value="dark"
                    defaultChecked
                    className="sr-only"
                  />
                </label>

                <label className="relative flex flex-col items-center p-4 bg-black/30 border-2 border-white/20 rounded-lg hover:bg-black/40 hover:border-white/30 transition-all duration-200 cursor-pointer">
                  <div className="flex items-center justify-center w-12 h-12 rounded-full bg-gray-200 mb-3">
                    <svg className="w-6 h-6 text-gray-800" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                  </div>
                  <div className="text-center">
                    <div className="text-white font-medium font-body mb-1">Light</div>
                    <div className="text-zinc-400 text-xs font-body">Light variant</div>
                  </div>
                  <input
                    type="radio"
                    name="theme"
                    value="light"
                    className="sr-only"
                  />
                </label>
              </div>
            </div>
          </div> */}

          {/* Account Section */}
          <div
            className={`relative ${themeConfig.card} rounded-2xl p-6 border ${themeConfig.border} transition-all duration-300 group overflow-hidden`}
            style={theme === 'dark' ? {
              boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
            } : {
              boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
            }}
          >
            <div className="relative z-10">
            <h2 className={`text-xl font-bold mb-6 font-title ${themeConfig.text}`}>{t('settings_account_title')}</h2>

            <button
              onClick={confirmSignOut}
              disabled={isSigningOut}
              className="w-full py-3 px-4 border border-red-600 bg-transparent text-red-500 hover:bg-red-600 hover:text-white rounded-lg transition-colors font-body text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSigningOut ? t('settings_logging_out') : t('settings_logout')}
            </button>
            </div>
          </div>

          <DeleteConfirmationModal
            deleteConfirm={showLogoutConfirmation ? { id: 1 } : null}
            isDeleting={isSigningOut}
            onCancel={() => setShowLogoutConfirmation(false)}
            onConfirmDelete={handleSignOut}
          />

          <UpdateStatusOverlay
            updateStatus={showSuccessPopup ? 'success' : 'idle'}
            updateProgress={100}
            updateMessage={t('settings_password_success_message')}
            onClose={() => setShowSuccessPopup(false)}
            successText={t('settings_password_updated')}
          />

          </div>
        </div>
      </div>
    </div>
  )
}
