'use client'

import { useThemeConfig } from '@/context/ThemeContext'
import { Button } from '@/components/ui'

interface PaginationControlsProps {
  currentPage: number
  totalPages: number
  itemsPerPage: number
  itemsPerPageOptions: number[]
  startIndex: number
  endIndex: number
  totalItems: number
  onPageChange: (page: number) => void
  onItemsPerPageChange: (itemsPerPage: number) => void
  onPreviousPage: () => void
  onNextPage: () => void
}

export default function PaginationControls({
  currentPage,
  totalPages,
  itemsPerPage,
  itemsPerPageOptions,
  startIndex,
  endIndex,
  totalItems,
  onPageChange,
  onItemsPerPageChange,
  onPreviousPage,
  onNextPage
}: PaginationControlsProps) {
  const themeConfig = useThemeConfig()

  return (
    <div className="mt-6 px-4">
      <div className={`flex flex-col sm:flex-row justify-between items-center gap-4 border-t ${themeConfig.divider} pt-4`}>
        {/* Pagination buttons - only show when there are multiple pages */}
        {totalPages > 1 && (
          <div className="flex items-center gap-1">
          {/* Previous page */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onPreviousPage}
            disabled={currentPage === 1}
            className="px-3 text-lg"
          >
            ‹
          </Button>

          {/* Page numbers */}
          {(() => {
            const pageNumbers = [];
            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
            
            // Adjust start if we're near the end
            if (endPage - startPage + 1 < maxVisiblePages) {
              startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            // Show first page and ellipsis if needed
            if (startPage > 1) {
              pageNumbers.push(
                <Button
                  key={1}
                  variant="ghost"
                  size="sm"
                  onClick={() => onPageChange(1)}
                  className="px-3"
                >
                  1
                </Button>
              );
              if (startPage > 2) {
                pageNumbers.push(
                  <span key="start-ellipsis" className={`px-2 ${themeConfig.textMuted}`}>
                    ...
                  </span>
                );
              }
            }

            // Show page numbers
            for (let page = startPage; page <= endPage; page++) {
              pageNumbers.push(
                <Button
                  key={page}
                  variant={currentPage === page ? "primary" : "ghost"}
                  size="sm"
                  onClick={() => onPageChange(page)}
                  className="px-3"
                >
                  {page}
                </Button>
              );
            }

            // Show ellipsis and last page if needed
            if (endPage < totalPages) {
              if (endPage < totalPages - 1) {
                pageNumbers.push(
                  <span key="end-ellipsis" className={`px-2 ${themeConfig.textMuted}`}>
                    ...
                  </span>
                );
              }
              pageNumbers.push(
                <Button
                  key={totalPages}
                  variant="ghost"
                  size="sm"
                  onClick={() => onPageChange(totalPages)}
                  className="px-3"
                >
                  {totalPages}
                </Button>
              );
            }

            return pageNumbers;
          })()}

          {/* Next page */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onNextPage}
            disabled={currentPage === totalPages}
            className="px-3 text-lg"
          >
            ›
          </Button>
          </div>
        )}

        {/* Page info */}
        <div className={`text-sm ${themeConfig.textMuted}`}>
          Showing {startIndex + 1}-{Math.min(endIndex, totalItems)} of {totalItems} items
        </div>

        {/* Items per page selector */}
        <div className="flex items-center gap-2">
          <select
            value={itemsPerPage}
            onChange={(e) => onItemsPerPageChange(Number(e.target.value))}
            className={`text-sm border rounded px-2 py-1 ${themeConfig.secondCard} ${themeConfig.text} ${themeConfig.border} ${themeConfig.borderHover} focus:outline-none focus:${themeConfig.borderActive}`}
          >
            {itemsPerPageOptions.map(option => (
              <option key={option} value={option}>
                {option} per page
              </option>
            ))}
          </select>
        </div>
      </div>
    </div>
  )
}