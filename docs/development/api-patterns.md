# API Development Patterns

This document outlines the standard patterns and conventions used for API development in the chhlat-bot project.

## Standard API Endpoint Structure

### Basic Pattern
```typescript
// Standard API endpoint structure
export async function POST(request: Request) {
  try {
    // 1. Auth verification with client identification
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // 2. Validate client ID if required
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }

    // 3. Cache check (if applicable)
    const cacheKey = `cache_${authId}`
    const cachedData = serverCache.get(cacheKey)
    if (cachedData) {
      return NextResponse.json({
        success: true,
        body: cachedData,
        cached: true
      })
    }

    // 4. Request validation
    const { data } = await request.json()
    if (!data) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Missing required data'
      }, { status: 400 })
    }

    // 5. Database operation via PostgreSQL using clientId directly
    const freshData = await queryOne(sql, [clientId, ...params])
    
    // 6. Cache update (if applicable)
    serverCache.set(cacheKey, freshData, 30)
    
    // 7. Return response
    return NextResponse.json({
      success: true,
      body: freshData,
      error_msg: null
    })

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { 
        success: false,
        body: null,
        error_msg: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    )
  }
}
```

## Authentication Pattern

### Server-Side Auth Verification
```typescript
// Import auth utility
import { verifyAuth } from '@/utils/auth'

// Use in all API endpoints with client identification
const { authenticated, authId, clientId } = await verifyAuth()
if (!authenticated || !authId) {
  return NextResponse.json({ 
    success: false,
    body: null,
    error_msg: 'Unauthorized'
  }, { status: 401 })
}

// Validate client ID if required for the operation
if (!clientId) {
  return NextResponse.json({
    success: false,
    body: null,
    error_msg: 'Client ID not found in authentication'
  }, { status: 400 })
}
```

### Client-Side Auth Context
```typescript
// Use auth context in components
import { useAuth } from '@/context/AuthContext'

const { user, session, signIn, signOut } = useAuth()
```

## Cache-First API Pattern

### Unified Cache Endpoint
```typescript
// /api/user/data/route.ts - Unified cache endpoint
export async function GET(request: NextRequest) {
  try {
    const { authenticated, authId } = await verifyAuth()
    if (!authenticated) return unauthorizedResponse()

    const { searchParams } = new URL(request.url)
    const cacheType = searchParams.get('cache') || 'dashboard'
    
    if (!['dashboard', 'knowledge'].includes(cacheType)) {
      return badRequestResponse('Invalid cache type')
    }

    const cacheKey = `${cacheType}_${authId}`
    const cachedData = serverCache.get(cacheKey)
    
    if (cachedData) {
      return NextResponse.json({
        success: true,
        body: cachedData,
        cached: true
      })
    }

    // Fetch fresh data from PostgreSQL
    const freshData = await queryOne(sql, [authId])
    
    // Cache for 30 minutes
    serverCache.set(cacheKey, freshData, 30)
    
    return NextResponse.json({
      success: true,
      body: freshData,
      cached: false
    })

  } catch (error) {
    return errorResponse(error)
  }
}
```

### Cache Update Pattern
```typescript
// Always update cache after mutations
const updateCache = (cacheKey: string, updatedData: any) => {
  const existingCache = serverCache.get(cacheKey)
  if (existingCache) {
    // Update existing cache only
    serverCache.set(cacheKey, updatedData, 30)
  }
  // Don't create new cache entries
}
```

## PostgreSQL Integration Pattern

### Direct Database Operations
```typescript
// Import PostgreSQL utilities
import { queryOne, query, transaction } from '@/lib/postgres'

// Standard database query
const result = await queryOne(
  'SELECT * FROM clients WHERE auth_id = $1',
  [authId]
)

// Multiple results
const results = await query(
  'SELECT * FROM photos WHERE client_id = $1 ORDER BY created_at DESC',
  [clientId]
)

// Transaction for atomic operations
const insertedFaqs = await transaction(async (client) => {
  const insertedResults = []
  
  for (const faq of faqBatch) {
    const result = await client.query(
      'INSERT INTO faqs (client_id, question, answer) VALUES ($1, $2, $3) RETURNING id',
      [clientId, faq.question, faq.answer]
    )
    insertedResults.push(result.rows[0])
  }
  
  return insertedResults
})
```

### Fire-and-Forget Webhook Pattern
```typescript
// Generate JWT token for webhook authentication (background only)
import { generateWebhookToken } from '@/utils/jwt'

const jwtToken = generateWebhookToken()

// Fire-and-forget webhook for analytics/external systems
if (webhookUrl) {
  fetch(webhookUrl, {
    method: 'POST',
    headers: { 
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${jwtToken}`
    },
    body: JSON.stringify({
      mode: 'faq',
      operation: 'add_batch',
      batch_id: batchId,
      faqs: successfulFaqs
    })
  }).catch(error => {
    // Log webhook errors but don't fail the request
    console.warn('Fire-and-forget webhook failed:', error)
  })
}
```

## Database Operation Patterns

### Transaction Pattern for Batch Operations
```typescript
// Use transactions for operations that must succeed or fail together
import { transaction } from '@/lib/postgres'

const results = await transaction(async (client) => {
  const insertedItems = []
  
  for (const item of batchItems) {
    const result = await client.query(
      'INSERT INTO faqs (client_id, question, answer) VALUES ($1, $2, $3) RETURNING id, created_at',
      [clientId, item.question, item.answer]
    )
    insertedItems.push(result.rows[0])
  }
  
  return insertedItems
})
```

### Error Handling with PostgreSQL
```typescript
// PostgreSQL-first error handling
try {
  const result = await queryOne(
    'SELECT * FROM photos WHERE client_id = $1 AND photo_id = $2',
    [clientId, photoId]
  )
  
  if (!result) {
    return NextResponse.json({
      success: false,
      body: null,
      error_msg: 'Photo not found'
    }, { status: 404 })
  }
  
  return NextResponse.json({
    success: true,
    body: result,
    error_msg: null
  })
  
} catch (error) {
  console.error('Database error:', error)
  return NextResponse.json({
    success: false,
    body: null,
    error_msg: 'Database operation failed'
  }, { status: 500 })
}
```

## Response Patterns

### Standard Response Structure
```typescript
// Success response
{
  success: true,
  body: data,
  error_msg: null,
  cached?: boolean // Optional cache indicator
}

// Error response
{
  success: false,
  body: null,
  error_msg: string
}
```

### Response Helpers
```typescript
// Helper functions for consistent responses
const successResponse = (data: any, cached = false) => {
  return NextResponse.json({
    success: true,
    body: data,
    error_msg: null,
    ...(cached && { cached: true })
  })
}

const errorResponse = (error: Error | string, status = 500) => {
  return NextResponse.json({
    success: false,
    body: null,
    error_msg: error instanceof Error ? error.message : error
  }, { status })
}

const unauthorizedResponse = () => {
  return NextResponse.json({
    success: false,
    body: null,
    error_msg: 'Unauthorized'
  }, { status: 401 })
}

const badRequestResponse = (message: string) => {
  return NextResponse.json({
    success: false,
    body: null,
    error_msg: message
  }, { status: 400 })
}
```

## File Upload Pattern

### R2 File Upload
```typescript
// /api/file/photos/route.ts
export async function POST(request: Request) {
  try {
    const { authenticated, authId } = await verifyAuth()
    if (!authenticated) return unauthorizedResponse()

    const formData = await request.formData()
    const files = formData.getAll('files') as File[]
    const photoId = formData.get('photoId') as string

    // Validate files
    for (const file of files) {
      const validation = validateFile(file)
      if (!validation.valid) {
        return badRequestResponse(validation.error)
      }
    }

    // Upload to R2
    const uploadResults = await uploadToR2(files, `photos/${authId}/${photoId}`)
    
    return successResponse({
      photoUrls: uploadResults.urls,
      filePaths: uploadResults.paths
    })

  } catch (error) {
    return errorResponse(error)
  }
}
```

### File Validation
```typescript
// File validation helper
const validateFile = (file: File) => {
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png']
  const maxSize = 5 * 1024 * 1024 // 5MB

  if (!validTypes.includes(file.type)) {
    return { valid: false, error: 'Only JPEG and PNG files are allowed' }
  }

  if (file.size > maxSize) {
    return { valid: false, error: 'File size must be less than 5MB' }
  }

  return { valid: true }
}
```

## Security Patterns

### Path Validation
```typescript
// Validate file paths belong to authenticated user
const validateUserPath = (filePath: string, authId: string) => {
  const userPrefix = `photos/${authId}/`
  return filePath.startsWith(userPrefix)
}

// Usage in API
const filePaths = ['path1', 'path2']
const invalidPaths = filePaths.filter(path => !validateUserPath(path, authId))

if (invalidPaths.length > 0) {
  return badRequestResponse('Invalid file paths detected')
}
```

### User-Scoped Queries
```typescript
// Always include user scoping in database queries using clientId from auth
const { authenticated, authId, clientId } = await verifyAuth()

const sql = `
  SELECT * FROM photos 
  WHERE client_id = $1 AND photo_id = $2
`
const params = [clientId, photoId] // clientId from verifyAuth(), not cache

// Never allow unscoped queries
// BAD: SELECT * FROM photos WHERE photo_id = $1
// GOOD: SELECT * FROM photos WHERE client_id = $1 AND photo_id = $2
// BEST: Use clientId from verifyAuth() for optimal performance
```

## Batch Processing Pattern

### FAQ Batch Processing with PostgreSQL
```typescript
// /api/knowledge/add-batch/route.ts
export async function POST(request: Request) {
  try {
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !clientId) return unauthorizedResponse()

    const formData = await request.formData()
    const faqBatch = JSON.parse(formData.get('faqBatch') as string)
    
    // Validate batch size
    const MAX_BATCH_SIZE = 10
    if (faqBatch.length > MAX_BATCH_SIZE) {
      return badRequestResponse(`Batch size exceeds limit of ${MAX_BATCH_SIZE}`)
    }

    // Process audio FAQs in parallel (no chunking)
    const audioFaqs = faqBatch.filter(faq => faq.isAudioAnswer)
    const textFaqs = faqBatch.filter(faq => !faq.isAudioAnswer)
    
    // Parallel R2 upload for audio files
    const audioResults = await Promise.allSettled(
      audioFaqs.map(faq => processAudioFaq(faq, formData, authId))
    )
    
    // Combine all processed FAQs
    const allProcessedFaqs = [...textFaqs, ...audioResults.map(r => 
      r.status === 'fulfilled' ? r.value : r.reason
    )]
    
    const successfulFaqs = allProcessedFaqs.filter(faq => faq.success)
    
    // Insert to PostgreSQL using transaction
    const insertedFaqs = await transaction(async (client) => {
      const results = []
      for (const faq of successfulFaqs) {
        const result = await client.query(
          `INSERT INTO faqs (client_id, faq_id, question, answer, audio_url, is_visible) 
           VALUES ($1, $2, $3, $4, $5, $6) RETURNING id, faq_id, created_at`,
          [clientId, faq.faq_id, faq.question, faq.answer, faq.audio_url, true]
        )
        results.push(result.rows[0])
      }
      return results
    })
    
    // Fire-and-forget webhook for analytics
    if (webhookUrl && successfulFaqs.length > 0) {
      sendAnalyticsWebhook(successfulFaqs, clientId).catch(console.warn)
    }

    return successResponse({
      items_processed: successfulFaqs.length,
      items_failed: allProcessedFaqs.length - successfulFaqs.length,
      inserted_faqs: insertedFaqs
    })

  } catch (error) {
    return errorResponse(error)
  }
}
```

## Error Handling Patterns

### Comprehensive Error Handling
```typescript
try {
  // API logic here
} catch (error: unknown) {
  console.error('API Error:', error)
  
  // Handle different error types
  if (error instanceof ValidationError) {
    return badRequestResponse(error.message)
  }
  
  if (error instanceof AuthError) {
    return unauthorizedResponse()
  }
  
  if (error instanceof DatabaseError) {
    return errorResponse('Database operation failed', 500)
  }
  
  // Generic error fallback
  return errorResponse(
    error instanceof Error ? error.message : 'Internal server error'
  )
}
```

### Input Validation
```typescript
// Validate required fields
const validateRequiredFields = (data: any, fields: string[]) => {
  const missing = fields.filter(field => !data[field])
  if (missing.length > 0) {
    throw new ValidationError(`Missing required fields: ${missing.join(', ')}`)
  }
}

// Usage
validateRequiredFields(requestData, ['photo_id', 'photo_url'])
```

## Performance Patterns

### Request Deduplication
```typescript
// Global request promises to prevent duplicate calls
let activeRequests = new Map<string, Promise<any>>()

const deduplicateRequest = async (key: string, requestFn: () => Promise<any>) => {
  if (activeRequests.has(key)) {
    return await activeRequests.get(key)
  }
  
  const promise = requestFn()
  activeRequests.set(key, promise)
  
  try {
    const result = await promise
    return result
  } finally {
    activeRequests.delete(key)
  }
}
```

### PostgreSQL Connection Pooling
```typescript
// Direct PostgreSQL connection pooling (built-in)
import { getPool, query, queryOne } from '@/lib/postgres'

// Connection pool is automatically managed
const executeQuery = async (sql: string, params: any[]) => {
  try {
    // Pool handles connection management automatically
    const result = await query(sql, params)
    return result.rows
  } catch (error) {
    console.error('Database query error:', error)
    throw error
  }
}

// Single result queries
const executeSingleQuery = async (sql: string, params: any[]) => {
  try {
    const result = await queryOne(sql, params)
    return result
  } catch (error) {
    console.error('Database query error:', error)
    throw error
  }
}

// Health check for connection pool
const checkDatabaseHealth = async () => {
  try {
    const result = await queryOne('SELECT 1 as health')
    return result?.health === 1
  } catch (error) {
    console.error('Database health check failed:', error)
    return false
  }
}
```

## Best Practices

1. **Always authenticate** before processing requests
2. **Use cache-first strategy** for frequently accessed data
3. **Validate all inputs** before processing
4. **Use consistent response format** across all endpoints
5. **Handle errors gracefully** with appropriate HTTP status codes
6. **Log errors** for debugging and monitoring
7. **Use TypeScript** for better type safety
8. **Implement request deduplication** for performance
9. **Use user-scoped queries** for security
10. **Test with both success and error scenarios**

## Common Patterns by Feature

### Dashboard APIs
- Use `/api/user/data?cache=dashboard` for cached data
- Include usage statistics and client info
- Implement request deduplication

### Knowledge APIs
- Use `/api/user/data?cache=knowledge` for cached data
- Include photos array and knowledge stats
- Support batch operations for FAQs with PostgreSQL transactions
- Use fire-and-forget webhooks for analytics only

### File APIs
- Always validate file types and sizes
- Use user-scoped path validation
- Support batch uploads and deletions

### Authentication APIs
- Use secure session management
- Implement proper logout cleanup
- Handle auth errors gracefully