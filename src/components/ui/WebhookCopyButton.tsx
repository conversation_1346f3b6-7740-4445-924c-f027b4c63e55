'use client'

import { useState } from 'react'
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'

interface WebhookCopyButtonProps {
  id: string
  displayText: string
  title?: string
  onCopy: (id: string) => Promise<void>
  isCopied: boolean
  className?: string
}

export default function WebhookCopyButton({
  id,
  displayText,
  title,
  onCopy,
  isCopied,
  className = ''
}: WebhookCopyButtonProps) {
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()

  const handleClick = async () => {
    await onCopy(id)
  }

  return (
    <button
      className={`w-full ${themeConfig.secondCard} border ${isCopied ? 'border-jade-purple' : themeConfig.border} rounded-lg px-4 py-2.5 text-sm ${themeConfig.textSecondary} font-body flex items-center justify-between ${isCopied ? '' : themeConfig.borderHover} transition-colors cursor-pointer ${className}`}
      onClick={handleClick}
      title={title || `Click to copy ${displayText}`}
    >
      <span className={`${displayText.startsWith('https://') ? 'text-jade-purple' : ''} truncate block`}>
        {displayText}
      </span>
      <span className={`${isCopied ? 'text-jade-purple' : themeConfig.textSecondary} ml-2 flex-shrink-0 w-5 h-5 flex items-center justify-center`}>
        {isCopied ? (
          <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
            <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
          </svg>
        ) : (
          <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
            <path strokeLinecap="round" strokeLinejoin="round" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
          </svg>
        )}
      </span>
    </button>
  )
}