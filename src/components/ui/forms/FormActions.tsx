'use client'

import { useLanguage } from '@/context/LanguageContext'
import { Button } from '@/components/ui'

interface FormActionsProps {
  onCancel: () => void
  onSubmit?: () => void
  isLoading?: boolean
  isDisabled?: boolean
  loadingText?: string
  submitText?: string
  cancelText?: string
  submitVariant?: 'primary' | 'secondary' | 'danger' | 'ghost'
  className?: string
}

export default function FormActions({
  onCancel,
  onSubmit,
  isLoading = false,
  isDisabled = false,
  loadingText,
  submitText,
  cancelText,
  submitVariant = 'primary',
  className = ""
}: FormActionsProps) {
  const { t } = useLanguage()

  return (
    <div className={`flex justify-end space-x-3 ${className}`}>
      <Button
        type="button"
        onClick={onCancel}
        variant="secondary"
        size="sm"
        className="text-xs sm:text-base"
      >
        {cancelText || t('cancel')}
      </Button>
      <Button
        type={onSubmit ? "button" : "submit"}
        onClick={onSubmit}
        variant={submitVariant}
        size="sm"
        disabled={isDisabled}
        className="text-xs sm:text-base"
        isLoading={isLoading}
        loadingText={loadingText}
      >
        {submitText || t('submit')}
      </Button>
    </div>
  )
}