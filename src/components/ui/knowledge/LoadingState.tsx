'use client'

import { useLanguage } from '@/context/LanguageContext'
import { useThemeConfig } from '@/context/ThemeContext'

interface LoadingStateProps {
  isLoading: boolean
}

export default function LoadingState({ isLoading }: LoadingStateProps) {
  const { t } = useLanguage()
  const themeConfig = useThemeConfig()

  if (!isLoading) return null

  return (
    <>
      {/* Loading header */}
      <div className="py-8 flex justify-center items-center">
        <div className="flex flex-col items-center space-y-4">
          {/* Main loading spinner */}
          <div className="relative">
            <div className="w-12 h-12 border-4 border-jade-purple/20 border-t-jade-purple rounded-full animate-spin"></div>
            <div className="absolute inset-0 w-12 h-12 border-4 border-transparent border-r-jade-purple/40 rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>
          </div>

          {/* Loading text with animation */}
          <div className="text-center">
            <p className={`${themeConfig.text} text-lg font-medium mb-1`}>{t('kb_loading_questions')}</p>
          </div>

          {/* Progress indicator */}
          <div className={`w-48 h-1 ${themeConfig.skeletonElement} rounded-full overflow-hidden`}>
            <div className="h-full bg-gradient-to-r from-jade-purple to-jade-purple-dark rounded-full animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* Skeleton loading for FAQ list */}
      <div className="space-y-3">
        {/* Column headers skeleton */}
        <div className={`flex border-b ${themeConfig.divider} py-3 font-semibold ${themeConfig.textSecondary} font-body`}>
          <div className="w-[6%] px-2"></div>
          <div className="w-[35%] px-2">
            <div className={`h-4 ${themeConfig.skeletonElement} rounded animate-pulse`}></div>
          </div>
          <div className="w-[35%] px-2">
            <div className={`h-4 ${themeConfig.skeletonElement} rounded animate-pulse`}></div>
          </div>
          <div className="w-[15%] px-2">
            <div className={`h-4 ${themeConfig.skeletonElement} rounded animate-pulse`}></div>
          </div>
          <div className="w-[9%] px-2"></div>
        </div>

        {/* Skeleton FAQ rows */}
        {Array.from({ length: 8 }).map((_, index) => (
          <div key={index} className={`flex border-b ${themeConfig.divider} py-3 items-center`}>
            <div className="w-[6%] px-2">
              <div className={`h-4 w-6 ${themeConfig.skeletonElement} rounded animate-pulse`}></div>
            </div>
            <div className="w-[35%] px-2">
              <div className={`h-4 ${themeConfig.skeletonElement} rounded animate-pulse`} style={{ animationDelay: `${index * 0.1}s` }}></div>
            </div>
            <div className="w-[35%] px-2">
              <div className={`h-4 ${themeConfig.skeletonElement} rounded animate-pulse`} style={{ animationDelay: `${index * 0.15}s` }}></div>
            </div>
            <div className="w-[15%] px-2">
              <div className={`h-8 w-8 ${themeConfig.skeletonElement} rounded animate-pulse`} style={{ animationDelay: `${index * 0.2}s` }}></div>
            </div>
            <div className="w-[9%] px-2 flex flex-col space-y-1">
              <div className={`h-6 w-6 ${themeConfig.skeletonElement} rounded animate-pulse`} style={{ animationDelay: `${index * 0.25}s` }}></div>
              <div className={`h-6 w-6 ${themeConfig.skeletonElement} rounded animate-pulse`} style={{ animationDelay: `${index * 0.3}s` }}></div>
            </div>
          </div>
        ))}
      </div>
    </>
  )
}