'use client'

import { useLanguage } from '@/context/LanguageContext'
import { useThemeConfig } from '@/context/ThemeContext'
import { LinkButton } from '@/components/ui'

interface DashboardHeaderProps {
  backHref?: string
  titleKey: string
}

export default function DashboardHeader({ 
  backHref = '/dashboard/knowledge', 
  titleKey 
}: DashboardHeaderProps) {
  const { t } = useLanguage()
  const themeConfig = useThemeConfig()

  return (
    <div className="mb-6">
      <div className="flex items-center justify-between mb-2">
        <LinkButton
          href={backHref}
          variant="secondary"
          size="sm"
          className="inline-flex items-center text-sm"
          leftIcon={
            <svg
              className="w-4 h-4 -ml-0.5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          }
        >
          {t('back')}
        </LinkButton>

        <h1 className={`text-2xl md:text-3xl text-jade-purple-dark font-extrabold font-title ${themeConfig.text}`}>
          {t(titleKey)}
        </h1>

        {/* Empty div for balanced spacing */}
        <div className="w-10"></div>
      </div>
    </div>
  )
}