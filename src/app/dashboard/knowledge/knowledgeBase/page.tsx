'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import { motion } from 'framer-motion'
import Dashboard<PERSON>ooter from '@/components/DashboardFooter'
import { createClientComponentClient } from '@/utils/supabase/client'

import { useKnowledgeData, updateFaqCountInCache } from '@/hooks/useOptimizedData'
import { FaMicrophone } from 'react-icons/fa'
import { v4 as uuidv4 } from 'uuid'
// Webhook calls are now handled by the update API endpoint
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'
import { optimizeGalleryImage } from '@/utils/imageOptimization'
import { Button, LinkButton, AudioPlayer, DashboardHeader, AnswerEditField } from '@/components/ui'
import {
  ViewModal,
  EditModal,
  UpdateConfirmationModal,
  CancelConfirmationModal,
  DeleteConfirmationModal,
  AudioModeWarningModal,
  DeleteAudioConfirmationModal,
  ImageGalleryModal
} from '@/components/ui/modals'
import {
  LoadingState,
  PaginationControls,
  UpdateStatusOverlay,
  PhotoSearchBar,
  SelectedPhotoDisplay,
  HelpSection
} from '@/components/ui/knowledge'
import type { PhotoData } from '@/components/ui/knowledge/PhotoSearchBar'

// Optimized PhotoThumbnail component for fast loading without Vercel Image
const PhotoThumbnail = ({
  photo,
  className = "w-10 h-10",
  onClick
}: {
  photo: { photo_url: string[] | null, photo_id: string },
  className?: string,
  onClick?: () => void
}) => {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()

  return (
    <div
      className={`${className} ${themeConfig.skeletonCard} rounded overflow-hidden flex-shrink-0 relative ${onClick ? 'cursor-pointer' : ''}`}
      onClick={onClick}
    >
      {photo.photo_url && photo.photo_url.length > 0 && !imageError ? (
        <>
          {/* Loading placeholder - no animation */}
          <div className={`absolute inset-0 ${themeConfig.skeletonCard} transition-opacity duration-200 ${imageLoaded ? 'opacity-0' : 'opacity-100'}`} />

          {/* Actual image */}
          <img
            src={photo.photo_url[0]}
            alt={photo.photo_id}
            className={`w-full h-full object-cover transition-opacity duration-200 ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
            loading="lazy"
            decoding="async"
            onLoad={() => setImageLoaded(true)}
            onError={() => {
              setImageError(true)
              setImageLoaded(true)
            }}
          />
        </>
      ) : (
        <div className={`w-full h-full flex items-center justify-center ${themeConfig.skeletonCard} ${themeConfig.textMuted} text-xs`}>
          <svg width="24" height="18" viewBox="0 0 24 18" fill="currentColor">
            <rect width="24" height="18" rx="2" fill="currentColor" opacity="0.3"/>
            <path d="M3 13L7 9L11 13L15 6L21 12V15H3V13Z" fill="currentColor" opacity="0.6"/>
            <circle cx="7" cy="6" r="2" fill="currentColor" opacity="0.6"/>
          </svg>
        </div>
      )}
    </div>
  )
}

// Types for our database records
type FAQ = {
  id: number
  faq_id: string // Unique identifier for the FAQ
  question: string
  answer: string
  question_p?: string
  answer_p?: string
  created_at: string
  photo_url?: any // JSONB field from database
  photo_id?: string // Product name field
  audio_url?: string // URL to the audio file
  is_audio_answer?: boolean // Legacy audio answer flag (keeping for backward compatibility)
  audio_file_path?: string // Add this line
}


export default function KnowledgeBasePage() {
  const supabase = createClientComponentClient()
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()

  // Use knowledge cache for photos only
  const { data: knowledgeData, loading: isKnowledgeLoading } = useKnowledgeData()
  const photosData = knowledgeData?.photos

  // Add state to store all photos for local search
  const [allPhotos, setAllPhotos] = useState<Array<{
    id: number,
    photo_id: string,
    photo_url: string[] | null,
    photo_file_path: string[] | null
  }>>([])

  // Update allPhotos state when data changes
  useEffect(() => {
    if (photosData) {
      setAllPhotos(photosData)
    }
  }, [photosData])

  // Viewing popup state
  const [viewingItem, setViewingItem] = useState<{field: 'question' | 'answer', value: string} | null>(null)
  const viewModalRef = useRef<HTMLDivElement>(null)

  // Data states
  const [questions, setQuestions] = useState<FAQ[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)





  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(25)
  const itemsPerPageOptions = [25, 50, 100]

  // Search state
  const [searchQuery, setSearchQuery] = useState('')
  const [filteredQuestions, setFilteredQuestions] = useState<FAQ[]>([])

  // Recently added state
  const [recentQA, setRecentQA] = useState<FAQ | null>(null)
  const [isUpdating, setIsUpdating] = useState(false)
  const [isAudioAnswer, setIsAudioAnswer] = useState(false)
  const [initialIsAudioAnswer, setInitialIsAudioAnswer] = useState(false)
  const [isValidatingAudio, setIsValidatingAudio] = useState<boolean>(false)
  const [audioValidation, setAudioValidation] = useState<{valid: boolean, duration?: number, file_id?: string, previewUrl?: string, error?: string} | null>(null)
  const [showAudioModeWarning, setShowAudioModeWarning] = useState(false)
  const [originalAudioCode, setOriginalAudioCode] = useState<string | null>(null)
  const [originalAudioFilePath, setOriginalAudioFilePath] = useState<string | null>(null)
  const [showDeleteAudioConfirm, setShowDeleteAudioConfirm] = useState(false)
  const [selectedAudioId, setSelectedAudioId] = useState<number | null>(null)
  const [isAudioPlaying, setIsAudioPlaying] = useState(false)
  
  // Separate audio state for Recently Added section to prevent race conditions
  const [recentlyAddedAudioId, setRecentlyAddedAudioId] = useState<number | string | null>(null)
  const [isRecentlyAddedAudioPlaying, setIsRecentlyAddedAudioPlaying] = useState(false)

  // Photo search state
  const [selectedPhoto, setSelectedPhoto] = useState<PhotoData | null>(null)
  const [photoSearchQuery, setPhotoSearchQuery] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const [isPhotoLoading, setIsPhotoLoading] = useState(false)
  const [photoSearchResults, setPhotoSearchResults] = useState<PhotoData[]>([])
  const [showPhotoResults, setShowPhotoResults] = useState(false)

  // Edit functionality
  const [editingItem, setEditingItem] = useState<{id: number, field: 'question' | 'answer', value: string} | null>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const modalRef = useRef<HTMLDivElement>(null)

  // Track if textarea has been focused for mobile behavior
  const [hasFocusedInput, setHasFocusedInput] = useState(false)

  // State for update status
  const [updateStatus, setUpdateStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  const [updateMessage, setUpdateMessage] = useState('')
  const [updateProgress, setUpdateProgress] = useState(0)
  const [showConfirmation, setShowConfirmation] = useState(false)
  const confirmModalRef = useRef<HTMLDivElement>(null)

  // Delete confirmation
  const [deleteConfirm, setDeleteConfirm] = useState<{id: number} | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const deleteModalRef = useRef<HTMLDivElement>(null)

  // Add a reference for the update section
  const updateSectionRef = useRef<HTMLDivElement>(null)

  


  // Add state for cancel confirmation
  const [showCancelConfirmation, setShowCancelConfirmation] = useState(false)
  const cancelConfirmModalRef = useRef<HTMLDivElement>(null)

  // Add state for image gallery
  const [imageGallery, setImageGallery] = useState<{urls: string[], currentIndex: number} | null>(null)
  const imageGalleryRef = useRef<HTMLDivElement>(null)

  // Add touch support for image gallery
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  // Navigation functions for image gallery
  const showPreviousImage = useCallback(() => {
    if (!imageGallery || imageGallery.urls.length <= 1) return;
    const newIndex = (imageGallery.currentIndex - 1 + imageGallery.urls.length) % imageGallery.urls.length;
    setImageGallery({ ...imageGallery, currentIndex: newIndex });
  }, [imageGallery]);

  const showNextImage = useCallback(() => {
    if (!imageGallery || imageGallery.urls.length <= 1) return;
    const newIndex = (imageGallery.currentIndex + 1) % imageGallery.urls.length;
    setImageGallery({ ...imageGallery, currentIndex: newIndex });
  }, [imageGallery]);

  
  // Reference for FAQ list container (for auto-scroll)
  const faqListRef = useRef<HTMLDivElement>(null);

  // Photos data is now handled by the usePhotosData hook


  // Removed fetchFaqCount - now using dashboard cache

  // Fetch questions via API (Security Enhanced)
  const fetchQuestions = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // Call knowledge lists API
      const response = await fetch('/api/knowledge/lists')
      let responseData = await response.json()

      if (!response.ok) {
        throw new Error(responseData.error_msg || 'Failed to fetch knowledge items')
      }

      if (!responseData.success) {
        throw new Error(responseData.error_msg || 'Failed to fetch knowledge items')
      }

      const processedData = responseData.body || []

      setQuestions(processedData);
      setFilteredQuestions(processedData);

      // FAQ count is now managed by dashboard cache
    } catch (err: any) {
      console.error('Error fetching questions:', err);
      setError(err.message || 'Failed to load questions');
    } finally {
      setIsLoading(false);
    }
  }

  // Incremental update functions for better performance
  const updateQuestionInState = (updatedQuestion: any) => {
    // Move the updated question to the top of the list
    setQuestions(prev => {
      const filtered = prev.filter(q => q.id !== updatedQuestion.id);
      return [updatedQuestion, ...filtered];
    });
    
    // Also update filtered questions while maintaining the current filter
    setFilteredQuestions(prev => {
      const filtered = prev.filter(q => q.id !== updatedQuestion.id);
      return [updatedQuestion, ...filtered];
    });
  }

  const removeQuestionFromState = (questionId: number) => {
    setQuestions(prev => prev.filter(q => q.id !== questionId))
    setFilteredQuestions(prev => prev.filter(q => q.id !== questionId))
  }

  // Client-side search function (no server search needed since we load all data)
  const performClientSearch = useCallback((query: string) => {
    if (!query.trim()) {
      setFilteredQuestions(questions);
      return;
    }

    const queryLower = query.toLowerCase();
    const filtered = questions.filter(q =>
      (q.question_p || q.question || '').toLowerCase().includes(queryLower) ||
      (q.answer_p || q.answer || '').toLowerCase().includes(queryLower)
    );
    setFilteredQuestions(filtered);
  }, [questions]);

  // Pagination functions
  const totalPages = Math.ceil(filteredQuestions.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentPageData = filteredQuestions.slice(startIndex, endIndex);

  const goToPage = (page: number) => {
    setCurrentPage(page);
    // Auto-scroll to top of FAQ list
    if (faqListRef.current) {
      faqListRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      goToPage(currentPage + 1);
    }
  };

  const goToPreviousPage = () => {
    if (currentPage > 1) {
      goToPage(currentPage - 1);
    }
  };

  const changeItemsPerPage = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page
    // Auto-scroll to top of FAQ list
    if (faqListRef.current) {
      faqListRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  // Reset to first page when search results change
  useEffect(() => {
    setCurrentPage(1);
  }, [filteredQuestions.length]);

  // Function to validate audio code
  const validateAudioCode = async (audioCode: string) => {
    if (!audioCode.trim()) {
      setAudioValidation(null)
      return
    }

    setIsValidatingAudio(true)
    try {
      const response = await fetch('/api/audio/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ audioCode: audioCode.trim() })
      })

      if (response.ok && response.headers.get('X-Audio-Valid') === 'true') {
        // Response is processed audio blob
        const audioBlob = await response.blob()
        const previewUrl = URL.createObjectURL(audioBlob)

        const duration = parseInt(response.headers.get('X-Audio-Duration') || '0')
        const fileId = response.headers.get('X-Audio-FileId') || ''

        if (duration > 0) {
          setAudioValidation({
            valid: true,
            duration: duration,
            file_id: fileId,
            previewUrl: previewUrl
          })
        } else {
          // Invalid duration
          URL.revokeObjectURL(previewUrl)
          setAudioValidation(null)
          if (recentQA) {
            setRecentQA({ ...recentQA, answer: '' })
          }
          setUpdateMessage(t('audio_code_incorrect'))
          setUpdateStatus('error')
          setTimeout(() => setUpdateStatus('idle'), 1500)
        }
      } else {
        // Handle JSON error response
        const errorData = await response.json()
        console.error('Audio validation failed:', errorData.error_msg)

        // Clear back to normal audio mode
        setAudioValidation(null)
        if (recentQA) {
          setRecentQA({ ...recentQA, answer: '' })
        }
        setUpdateMessage(t('audio_code_incorrect'))
        setUpdateStatus('error')
        setTimeout(() => setUpdateStatus('idle'), 1500)
      }
    } catch (error) {
      console.error('Audio validation error:', error)
      // Clear back to normal audio mode on error
      setAudioValidation(null)
      if (recentQA) {
        setRecentQA({ ...recentQA, answer: '' })
      }
      // Show error message for 1500ms
      setUpdateMessage(t('audio_code_incorrect'))
      setUpdateStatus('error')
      setTimeout(() => setUpdateStatus('idle'), 1500)
    } finally {
      setIsValidatingAudio(false)
    }
  }

  // Function to confirm switching to audio mode
  const confirmAudioMode = () => {
    if (recentQA) {
      setRecentQA({ ...recentQA, answer: '' });
    }
    setIsAudioAnswer(true);
    setShowAudioModeWarning(false);
    // Clean up any existing audio validation
    if (audioValidation?.previewUrl) {
      URL.revokeObjectURL(audioValidation.previewUrl);
    }
    setAudioValidation(null);
  }

  // Handle audio delete confirmation for validated codes
  const handleConfirmAudioDelete = () => {
    if (audioValidation?.previewUrl) {
      URL.revokeObjectURL(audioValidation.previewUrl)
    }
    setAudioValidation(null)
    if (recentQA) {
      setRecentQA({ ...recentQA, answer: '' })
    }
    setShowDeleteAudioConfirm(false)
    // Note: Stay in audio mode (don't change isAudioAnswer) - same as knowledge page
  }

  // Audio playback handlers for validated audio codes
  const handleValidatedAudioClick = () => {
    // Reset main Knowledge Base section audio when playing validated audio
    setSelectedAudioId(null);
    setIsAudioPlaying(false);
    setRecentlyAddedAudioId(null);
    setIsRecentlyAddedAudioPlaying(false);

    // For validated audio, we use a special ID
    const validatedAudioId = 'validated-audio';
    if (recentlyAddedAudioId === validatedAudioId) {
      // Same audio clicked - toggle play/pause
      setIsRecentlyAddedAudioPlaying(!isRecentlyAddedAudioPlaying);
    } else {
      // Start playing validated audio
      setRecentlyAddedAudioId(validatedAudioId);
      setIsRecentlyAddedAudioPlaying(true);
    }
  }

  // Audio control function for main Knowledge Base list
  const handleAudioClick = (id: number) => {
    // Reset Recently Added section audio when playing in main list
    setRecentlyAddedAudioId(null);
    setIsRecentlyAddedAudioPlaying(false);
    
    if (selectedAudioId === id) {
      // Same audio clicked - toggle play/pause (preserve position)
      setIsAudioPlaying(!isAudioPlaying);
    } else {
      // Different audio clicked - select new one and start playing (reset previous)
      setSelectedAudioId(id);
      setIsAudioPlaying(true);
    }
  };

  // Separate handler for Recently Added section audio
  const handleRecentlyAddedAudioClick = (id: number) => {
    // Reset main Knowledge Base section audio when playing in Recently Added
    setSelectedAudioId(null);
    setIsAudioPlaying(false);
    
    if (recentlyAddedAudioId === id) {
      // Same audio clicked - toggle play/pause (preserve position)
      setIsRecentlyAddedAudioPlaying(!isRecentlyAddedAudioPlaying);
    } else {
      // Different audio clicked - select new one and start playing (reset previous)
      setRecentlyAddedAudioId(id);
      setIsRecentlyAddedAudioPlaying(true);
    }
  };

  const handleAudioError = (error: string) => {
    console.error('Audio error:', error);
    setSelectedAudioId(null);
    setIsAudioPlaying(false);
  };

  // Load questions on mount
  useEffect(() => {
    if (questions.length === 0) {
      fetchQuestions()
    }
  }, []) // Load questions once on mount

  // Effect to filter questions based on search query (client-side only)
  useEffect(() => {
    if (!questions.length) return;
    performClientSearch(searchQuery);
  }, [searchQuery, questions, performClientSearch])



  // Calculate visible items for virtual scrolling (now handled by global scroll)



  const handleViewItem = (field: 'question' | 'answer', value: string) => {
    setViewingItem({ field, value })
  }

  // Move item to Recently Added section for editing
  const handleMoveToRecentlyAdded = (qa: FAQ) => {
    // Use the correct database fields
    const questionText = qa.question_p || qa.question || '';
    const answerText = qa.answer_p || qa.answer || '';
    
    // Clear existing items and add just this one
    setRecentQA({
      id: qa.id,
      faq_id: qa.faq_id, // Include the faq_id
      question: questionText,
      answer: qa.audio_url ? '' : answerText, // Set answer to empty if has audio URL
      audio_url: qa.audio_url, // Preserve audio URL for playback
      photo_url: qa.photo_url,
      photo_id: qa.photo_id,
      // These are not used in the recentQA context, but we include them to satisfy the type system
      created_at: qa.created_at,
      audio_file_path: qa.audio_file_path, // Add this line
    })

    // Check if the FAQ item has an audio URL
    const existingAudioState = !!qa.audio_url;
    setIsAudioAnswer(existingAudioState);
    setInitialIsAudioAnswer(existingAudioState);
    
    // Track original audio code and file path for change detection and cleanup
    setOriginalAudioCode(existingAudioState ? answerText : null);
    setOriginalAudioFilePath(qa.audio_file_path || null);
    
    // Reset audio validation when moving to Recently Added
    setAudioValidation(null);
    setIsValidatingAudio(false);
    
    // Don't auto-validate existing audio - only validate when user manually enters new code

    // If the item has an image, set it as the selected photo
    if (qa.photo_url) {
      const imageData = extractImageUrls(qa.photo_url);
      if (imageData.firstUrl) {
        setSelectedPhoto({
          id: qa.id,
          photo_id: qa.photo_id || "Attached Image",
          photo_url: imageData.allUrls,
          photo_file_path: null
        });
      }
    } else {
      // Reset selected photo if no image
      setSelectedPhoto(null);
    }

    // Reset photo search input and results
    setPhotoSearchQuery('');
    setPhotoSearchResults([]);
    setShowPhotoResults(false);

    // Scroll to update section
    setTimeout(() => {
      if (updateSectionRef.current) {
        updateSectionRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        })
      }
    }, 100)
  }



  // Explicit change detection functions following intro management pattern
  const hasAnswerChanged = useCallback(() => {
    if (!recentQA) return false;
    
    const originalQuestion = questions.find(q => q.id === recentQA.id);
    if (!originalQuestion) return false;
    
    const finalTextAnswer = isAudioAnswer ? '' : recentQA.answer;
    const originalTextAnswer = initialIsAudioAnswer ? '' : (originalQuestion.answer_p || originalQuestion.answer || '');
    
    return Boolean(
      finalTextAnswer !== originalTextAnswer ||
      (!originalTextAnswer && finalTextAnswer?.trim()) ||
      (originalTextAnswer && !finalTextAnswer?.trim())
    );
  }, [recentQA, isAudioAnswer, initialIsAudioAnswer, questions]);

  const hasAudioModeChanged = useCallback(() => {
    return isAudioAnswer !== initialIsAudioAnswer;
  }, [isAudioAnswer, initialIsAudioAnswer]);

  const hasAudioCodeChanged = useCallback(() => {
    if (!recentQA) return false;
    
    return isAudioAnswer && recentQA.answer && 
      recentQA.answer.trim() !== originalAudioCode;
  }, [recentQA, isAudioAnswer, originalAudioCode]);

  const hasAudioChanged = useCallback(() => {
    return hasAudioModeChanged() || hasAudioCodeChanged();
  }, [hasAudioModeChanged, hasAudioCodeChanged]);

  const hasPhotoChanged = useCallback(() => {
    if (!recentQA) return false;
    
    const originalQuestion = questions.find(q => q.id === recentQA.id);
    if (!originalQuestion) return false;
    
    const originalPhotoUrl = originalQuestion.photo_url;
    const currentPhotoUrl = selectedPhoto?.photo_url;
    
    return (
      // Photo added (didn't exist before but exists now)
      (selectedPhoto && !originalPhotoUrl) ||
      // Photo removed (existed before but not now)
      (!selectedPhoto && originalPhotoUrl) ||
      // Photo changed (different photo ID)
      (selectedPhoto && originalPhotoUrl &&
        originalQuestion.photo_id !== selectedPhoto.photo_id)
    );
  }, [recentQA, selectedPhoto, questions]);

  const isOnlyPhotoChanged = useCallback(() => {
    return hasPhotoChanged() && !hasAnswerChanged() && !hasAudioChanged();
  }, [hasPhotoChanged, hasAnswerChanged, hasAudioChanged]);

  const hasRealChanges = useCallback(() => {
    return hasAnswerChanged() || hasAudioChanged() || hasPhotoChanged();
  }, [hasAnswerChanged, hasAudioChanged, hasPhotoChanged]);

  // Legacy hasChanges function for backward compatibility
  const hasChanges = () => {
    return hasRealChanges();
  }

  // Check if the item has valid content following intro management pattern
  const hasValidContent = useCallback(() => {
    if (!recentQA) return false;

    // If there's an existing audio file, consider it valid
    if (recentQA.audio_url) {
      return true;
    }
    
    if (isAudioAnswer) {
      // For audio answers, check if validation passed
      return audioValidation?.valid || false;
    }
    
    // For text answers, check if there's content
    return recentQA.answer && recentQA.answer.trim() !== '';
  }, [recentQA, isAudioAnswer, audioValidation]);

  // Legacy hasValidAnswer function for backward compatibility
  const hasValidAnswer = () => {
    return hasValidContent();
  }

  // Prompt for confirmation before update
  const handleUpdate = () => {
    if (!recentQA) {
      setUpdateMessage("No questions to update. Add some questions and answers first.")
      setUpdateStatus('error')
      setTimeout(() => setUpdateStatus('idle'), 3000)
      return
    }

    if (!hasRealChanges()) {
      setUpdateMessage("No changes detected. Make some changes before updating.")
      setUpdateStatus('error')
      setTimeout(() => setUpdateStatus('idle'), 3000)
      return
    }

    if (!hasValidContent()) {
      if (isAudioAnswer) {
        setUpdateMessage("Please provide a valid audio code.")
      } else {
        setUpdateMessage("Please provide a text answer.")
      }
      setUpdateStatus('error')
      setTimeout(() => setUpdateStatus('idle'), 3000)
      return
    }

    setShowConfirmation(true)
  }

  // Save Q&As to Supabase in batches
  const saveQAToSupabase = async () => {
    setShowConfirmation(false)
    setIsUpdating(true)
    setUpdateStatus('loading')
    setUpdateProgress(0)

    try {

      // We now only have one item to update (recentQA is a single item, not an array)
      if (!recentQA) {
        throw new Error("No question to update")
      }

      const qa = recentQA; // recentQA is now a single item
      
      // Ensure faq_id exists - it should come from the database
      if (!qa.faq_id) {
        throw new Error("FAQ ID is required for updates. This FAQ may not have been properly loaded from the database.");
      }

      // Validate that we have actual changes to save
      if (!hasRealChanges()) {
        setUpdateMessage('No changes to save');
        setUpdateStatus('error');
        setTimeout(() => {
          setUpdateStatus('idle');
          setIsUpdating(false);
        }, 2000);
        return;
      }

      // For audio mode, validate audio code first
      if (isAudioAnswer && hasAudioChanged()) {
        if (!audioValidation?.valid) {
          setUpdateMessage('Please provide a valid audio code');
          setUpdateStatus('error');
          setTimeout(() => {
            setUpdateStatus('idle');
            setIsUpdating(false);
          }, 3000);
          return;
        }
      }

      // Get processed audio blob for upload (following knowledge page pattern)
      let processedAudioBlob: Blob | undefined
      if (isAudioAnswer && audioValidation?.valid && audioValidation.previewUrl) {
        try {
          setUpdateMessage('Processing audio...')
          // Convert preview URL back to blob for upload
          const response = await fetch(audioValidation.previewUrl)
          processedAudioBlob = await response.blob()
        } catch (error) {
          console.error('Error converting preview URL to blob:', error)
          setUpdateMessage('Failed to process audio file')
          setUpdateStatus('error')
          setTimeout(() => {
            setUpdateStatus('idle')
            setIsUpdating(false)
          }, 3000)
          return
        }
      }

      setUpdateProgress(20);
      setUpdateMessage('Processing changes...');

      // Prepare updateData following the intro management pattern
      const imageUrl = selectedPhoto?.photo_url;
      const imageName = selectedPhoto?.photo_id;
      
      const updateData: Record<string, unknown> = {
        photo_url: imageUrl === undefined ? null : imageUrl,
        photo_id: imageName === undefined ? null : imageName,
        is_audio: isAudioAnswer,
        onlyPhoto: isOnlyPhotoChanged()
      };

      // Conditional fields based on explicit change detection
      if (hasAnswerChanged()) {
        updateData.answer_p = isAudioAnswer ? '' : qa.answer;
      }

      // Check if question was changed
      const originalQuestion = questions.find(q => q.id === qa.id);
      const hasQuestionChanged = Boolean(
        qa.question && 
        originalQuestion && 
        qa.question !== (originalQuestion.question_p || originalQuestion.question)
      );

      if (hasQuestionChanged) {
        updateData.question_p = qa.question;
      }

      if (hasAudioChanged()) {
        updateData.isAudioAnswer = isAudioAnswer;
        updateData.audioCode = isAudioAnswer && qa.answer ? qa.answer.trim() : null;
        updateData.hasAudioCodeChanged = hasAudioCodeChanged();
        updateData.originalAudioFilePath = originalAudioFilePath;
        // Include audio duration for new audio codes
        updateData.audio_duration = isAudioAnswer && audioValidation?.valid ? audioValidation.duration : null;
      }

      if (hasPhotoChanged()) {
        updateData.fb_photo_atmid = null;
        updateData.ig_photo_atmid = null;
        updateData.tg_photo_atmid = null;
      }

      setUpdateProgress(40);
      setUpdateMessage('Saving to database...');

      // Prepare API request - use FormData if we have audio blob, otherwise JSON
      let updateResponse: Response
      if (processedAudioBlob) {
        // Use FormData to send audio blob
        const formData = new FormData()
        formData.append('faq_id', qa.faq_id)
        formData.append('updateData', JSON.stringify(updateData))
        formData.append('sector', knowledgeData?.sector || '')
        formData.append('lang', knowledgeData?.clientLang || '')
        formData.append('processedAudioBlob', processedAudioBlob, `audio_${qa.faq_id}.m4a`)
        
        updateResponse = await fetch('/api/knowledge/lists', {
          method: 'PUT',
          body: formData
        })
      } else {
        // Use JSON for non-audio updates
        updateResponse = await fetch('/api/knowledge/lists', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            faq_id: qa.faq_id,
            updateData: updateData,
            sector: knowledgeData?.sector,
            lang: knowledgeData?.clientLang
          })
        })
      }

      let updateResult = await updateResponse.json();
      
      // Process update result directly

      if (!updateResponse.ok) {
        throw new Error(updateResult.error || 'Failed to update record');
      }

      if (!updateResult.success) {
        throw new Error(updateResult.error_msg || 'Failed to update knowledge item');
      }

      setUpdateProgress(80);
      setUpdateMessage('Knowledge item updated successfully!');

      // Use the returned audio URL from the API if available
      const newAudioUrl = updateResult.body?.audio_url || qa.audio_url || null;
      const newAudioFilePath = updateResult.body?.audio_file_path || qa.audio_file_path || null;

      // Update local state with optimistic UI data
      const updatedQuestion = {
        ...qa,
        question: qa.question,
        answer: isAudioAnswer ? '' : qa.answer,
        question_p: qa.question,
        answer_p: isAudioAnswer ? '' : qa.answer,
        audio_url: isAudioAnswer ? newAudioUrl : null,
        audio_file_path: isAudioAnswer ? newAudioFilePath : null,
        photo_url: selectedPhoto?.photo_url || null,
        photo_id: selectedPhoto?.photo_id || null,
        updated_at: new Date().toISOString()
      };
      updateQuestionInState(updatedQuestion);

      setUpdateProgress(100);
      setUpdateMessage('Knowledge item updated!');

      // Set success status
      setUpdateStatus('success');
      
      // Clear the recently added item after successful update
      setRecentQA(null)
      setSelectedPhoto(null);
      
      // Reset audio state
      setIsAudioAnswer(false);
      setInitialIsAudioAnswer(false);

      // Auto-hide success message after 1.5 seconds
      setTimeout(() => {
        setUpdateStatus('idle')
        setIsUpdating(false)
      }, 1500)

    } catch (err: any) {
      console.error('Error updating knowledge base:', err)
      
      // Provide specific error messages based on error type
      let errorMessage = 'Failed to update knowledge base. Please try again.'
      if (err.message?.includes('Audio code')) {
        errorMessage = 'Invalid audio code. Please check the code and try again.'
      } else if (err.message?.includes('Unauthorized')) {
        errorMessage = 'Session expired. Please refresh the page and try again.'
      } else if (err.message?.includes('Network')) {
        errorMessage = 'Network error. Please check your connection and try again.'
      } else if (err.message) {
        errorMessage = err.message
      }
      
      setUpdateMessage(errorMessage);
      setUpdateStatus('error');
      setIsUpdating(false);
      
      // Auto-clear error status after 5 seconds
      setTimeout(() => {
        setUpdateStatus('idle');
      }, 5000);
    } finally {
      // Reset the appropriate saving state
      setTimeout(() => {
        setIsUpdating(false);
      }, 1000);
    }
  }

  // Helper function to extract URLs from the photo_url JSONB field
  const extractImageUrls = (imageData: any): {firstUrl: string | null, allUrls: string[]} => {
    if (!imageData) {
      return { firstUrl: null, allUrls: [] };
    }

    // If imageData is already a string, return it as the only URL
    if (typeof imageData === 'string') {
      return { firstUrl: imageData, allUrls: [imageData] };
    }

    // Handle case where imageData is an array of URLs
    if (Array.isArray(imageData)) {
      return {
        firstUrl: imageData.length > 0 ? imageData[0] : null,
        allUrls: imageData
      };
    }

    // Handle case where imageData has a url property and perhaps full_urls array
    if (imageData.url) {
      return {
        firstUrl: imageData.url,
        allUrls: imageData.full_urls || [imageData.url]
      };
    }

    // Handle case where imageData has just the full_urls array
    if (imageData.full_urls && Array.isArray(imageData.full_urls)) {
      return {
        firstUrl: imageData.full_urls.length > 0 ? imageData.full_urls[0] : null,
        allUrls: imageData.full_urls
      };
    }

    // Last attempt - try to find any string property that might be a URL
    const possibleUrls = Object.values(imageData).filter(val =>
      typeof val === 'string' && (val.startsWith('http') || val.startsWith('/'))
    ) as string[];

    return {
      firstUrl: possibleUrls.length > 0 ? possibleUrls[0] : null,
      allUrls: possibleUrls
    };
  }

  // Handle delete from Recently Added (just removes from recent items, not from database)
  const handleDelete = () => {
    if (!recentQA) return;

    // Find the original question from the questions array
    const originalQuestion = questions.find(q => q.id === recentQA.id);

    if (!originalQuestion) {
      // If we can't find the original (unlikely), just close the edit view
      setRecentQA(null);
      return;
    }

    // Currently editing text
    const isCurrentlyEditing = editingItem !== null;

    // Use explicit change detection functions
    const hasChanges = hasRealChanges() || isCurrentlyEditing;

    if (hasChanges) {
      // Show confirmation before canceling
      setShowCancelConfirmation(true);
    } else {
      // No changes, proceed with delete
      setRecentQA(null);
      // Reset audio state and validation
      setIsAudioAnswer(false);
      setInitialIsAudioAnswer(false);
      setAudioValidation(null);
      setIsValidatingAudio(false);
    }
  }

  // Handle cancel confirmation - proceed with deletion
  const confirmCancel = () => {
    setShowCancelConfirmation(false);
    setRecentQA(null);

    // Reset audio state and validation
    setIsAudioAnswer(false);
    setInitialIsAudioAnswer(false);
    // Clean up audio validation preview URL
    if (audioValidation?.previewUrl) {
      URL.revokeObjectURL(audioValidation.previewUrl);
    }
    setAudioValidation(null);
    setIsValidatingAudio(false);

    // Clean up any ongoing operations
    if (editingItem) {
      setEditingItem(null);
    }
  }



  // Handle removing image from an item in the update section
  const handleRemoveImage = () => {
    setRecentQA(null)
    // Reset audio state
    setIsAudioAnswer(false);
    setInitialIsAudioAnswer(false);
  }

  // Show delete confirmation
  const showDeleteConfirmation = (id: number) => {
    setDeleteConfirm({ id })
  }

  // Handle delete from database
  const handleDeleteFromDatabase = async () => {
    if (!deleteConfirm) return

    setIsDeleting(true)

    try {
      const { id } = deleteConfirm

      // Add a small delay for better UX
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Get the FAQ item to delete
      const faqToDelete = questions.find(q => q.id === id);
      if (!faqToDelete?.faq_id) {
        throw new Error('FAQ not found or missing faq_id');
      }

      // Delete the database record via API
      const deleteResponse = await fetch(`/api/knowledge/lists?faq_id=${faqToDelete.faq_id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          audio_file_path: faqToDelete.audio_file_path || null
        })
      })

      const deleteResult = await deleteResponse.json()

      if (!deleteResponse.ok) {
        console.error('Error deleting knowledge item:', deleteResult.error_msg)
        throw new Error(deleteResult.error_msg || 'Failed to delete question. Please try again.')
      }
      
      if (!deleteResult.success) {
        throw new Error(deleteResult.error_msg || 'Failed to delete question. Please try again.')
      }

      // Use incremental update instead of refetching all data
      removeQuestionFromState(id);

      // Update FAQ count in dashboard cache immediately
      // Calculate new count based on the current questions array after removal
      const newFaqCount = Math.max(questions.length - 1, 0);
      updateFaqCountInCache(newFaqCount);

      // Update success message
      setUpdateStatus('success')
      setUpdateMessage('Question deleted successfully.')

      // Reset status after a delay
      setTimeout(() => {
        setUpdateStatus('idle')
      }, 1500)

    } catch (error: any) {
      console.error('Error in deletion:', error)
      setUpdateStatus('error')
      setUpdateMessage(error.message || 'Failed to delete. Please try again.')
    } finally {
      setIsDeleting(false)
      setDeleteConfirm(null)
    }
  }



  // Refs for virtual scrolling and navigation
  const knowledgeBaseRef = useRef<HTMLDivElement>(null)
  const databaseSectionRef = useRef<HTMLDivElement>(null)

  // Scroll to database section
  const scrollToDatabase = () => {
    if (databaseSectionRef.current) {
      databaseSectionRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      })
    }
  }

  // Format date string
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })
  }

  // Add effect to focus textarea and set cursor at the end when editing
  useEffect(() => {
    if (editingItem && textareaRef.current && !hasFocusedInput) {
      // Check if device is desktop (not mobile/tablet) for auto-focus behavior
      const isDesktop = window.innerWidth >= 1024 && !('ontouchstart' in window)

      if (isDesktop) {
        // On desktop: auto-focus and set cursor at the end
        textareaRef.current.focus()
        const length = textareaRef.current.value.length
        textareaRef.current.setSelectionRange(length, length)
        setHasFocusedInput(true)
      }
      // On mobile/tablet: let user manually tap to position cursor and handle keyboard
      // Don't set hasFocusedInput to true yet - wait for user's first click
    }
  }, [editingItem, hasFocusedInput])

  // Reset focus tracking when modal closes
  useEffect(() => {
    if (!editingItem) {
      setHasFocusedInput(false);
    }
  }, [editingItem]);



  const handleStartEdit = (id: number, field: 'question' | 'answer', value: string) => {
    setEditingItem({ id, field, value })
    setHasFocusedInput(false)
    // Focus and set cursor at end will happen in useEffect
  }

  const handleSaveEdit = () => {
    if (editingItem) {
      // Always save the edit, even if field is empty
      if (recentQA) {
        setRecentQA({
          ...recentQA,
          [editingItem.field]: editingItem.value
        });
        
        // If this is an answer edit and we're in audio mode, validate the new code
        if (editingItem.field === 'answer' && isAudioAnswer && editingItem.value.trim()) {
          validateAudioCode(editingItem.value.trim());
        }
      }
    }
    setEditingItem(null)
  }








  // Add function to clear selected photo
  const handleClearSelectedPhoto = () => {
    setSelectedPhoto(null)
  }

  // Add function to search photos from Supabase
  const searchPhotos = async (query: string) => {
    if (!query.trim()) {
      setPhotoSearchResults([])
      setShowPhotoResults(false)
      return
    }

    setIsSearching(true)
    try {
      // Use the cached photos data from usePhotosData hook
      if (photosData) {
        const filteredPhotos = photosData
          .filter(photo => 
            photo && photo.photo_id && photo.photo_id.toLowerCase().includes(query.toLowerCase())
          )
          .slice(0, 5)

        setPhotoSearchResults(filteredPhotos)
        setShowPhotoResults(filteredPhotos.length > 0)
      } else {
        // If no cached data, search via API route
        const searchResponse = await fetch('/api/knowledge/search-photos', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ query, limit: 5 })
        })
        
        let searchData = await searchResponse.json()
        
        // Process search data directly
        
        if (searchData.success && searchData.body) {
          setPhotoSearchResults(searchData.body)
          setShowPhotoResults(searchData.body.length > 0)
        }
      }
    } catch (error) {
      console.error('Error searching photos:', error)
      setPhotoSearchResults([])
      setShowPhotoResults(false)
    } finally {
      setIsSearching(false)
    }
  }

  // Add function to clear photo search
  const clearPhotoSearch = () => {
    setPhotoSearchResults([]);
    setShowPhotoResults(false);
    setPhotoSearchQuery('');
  };

  // Add function to handle photo search input
  const handlePhotoSearch = (query: string) => {
    setPhotoSearchQuery(query)
    if (query.trim()) {
      searchPhotos(query)
    } else {
      clearPhotoSearch()
    }
  }

  // Add function to select a photo
  const handleSelectPhoto = (photo: PhotoData) => {
    // Show loading animation
    setIsPhotoLoading(true)
    
    // Clear search results and hide dropdown
    clearPhotoSearch()
    setPhotoSearchQuery('')

    // Add a small delay to show the loading animation
    setTimeout(() => {
      // Set the selected photo
      setSelectedPhoto({
        id: photo.id,
        photo_id: photo.photo_id,
        photo_url: photo.photo_url,
        photo_file_path: photo.photo_file_path
      })

      // Hide loading animation after a short delay
      setTimeout(() => {
        setIsPhotoLoading(false)
      }, 50)
    }, 100)
  }












  // Handle viewing an image in gallery modal
  const handleViewImage = (urls: string[] | null | any) => {
    let imageUrls: string[] = [];
    
    // Handle different input formats
    if (Array.isArray(urls)) {
      imageUrls = urls;
    } else if (typeof urls === 'string') {
      imageUrls = [urls];
    } else if (urls) {
      // If it's an object (legacy format), extract URLs
      const { allUrls } = extractImageUrls(urls);
      imageUrls = allUrls;
    }
    
    if (imageUrls.length > 0) {
      setImageGallery({
        urls: imageUrls,
        currentIndex: 0
      });
    } else {
      // Show a notification or alert that there are no images to display
      setUpdateStatus('error');
      setUpdateMessage('No images available for this item.');
      setTimeout(() => setUpdateStatus('idle'), 3000);
    }
  }

  // Keyboard navigation for image gallery (arrow keys and escape)
  useEffect(() => {
    function handleKeyDown(event: KeyboardEvent) {
      if (!imageGallery) return;

      if (event.key === 'ArrowLeft') {
        event.preventDefault();
        const prevIndex = (imageGallery.currentIndex - 1 + imageGallery.urls.length) % imageGallery.urls.length;
        setImageGallery({ ...imageGallery, currentIndex: prevIndex });
      } else if (event.key === 'ArrowRight') {
        event.preventDefault();
        const nextIndex = (imageGallery.currentIndex + 1) % imageGallery.urls.length;
        setImageGallery({ ...imageGallery, currentIndex: nextIndex });
      } else if (event.key === 'Escape') {
        event.preventDefault();
        setImageGallery(null);
      }
    }

    if (imageGallery) {
      document.addEventListener('keydown', handleKeyDown);
    }
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    }
  }, [imageGallery]);

  // Escape key functionality for View Modal
  useEffect(() => {
    function handleViewModalKeyDown(event: KeyboardEvent) {
      if (!viewingItem) return;

      if (event.key === 'Escape') {
        event.preventDefault();
        setViewingItem(null);
      }
    }

    if (viewingItem) {
      document.addEventListener('keydown', handleViewModalKeyDown);
    }
    return () => {
      document.removeEventListener('keydown', handleViewModalKeyDown);
    }
  }, [viewingItem]);

  // Disable page scroll when any popup/modal is open
  useEffect(() => {
    const hasAnyPopup = viewingItem || editingItem || showConfirmation || deleteConfirm ||
                       updateStatus === 'loading' || updateStatus === 'success' || updateStatus === 'error' ||
                       showCancelConfirmation || showAudioModeWarning || showDeleteAudioConfirm || imageGallery;

    if (hasAnyPopup) {
      document.body.style.overflow = 'hidden';
      // Pause any playing audio when modal opens (both sections)
      setSelectedAudioId(null);
      setIsAudioPlaying(false);
      setRecentlyAddedAudioId(null);
      setIsRecentlyAddedAudioPlaying(false);
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [viewingItem, editingItem, showConfirmation, deleteConfirm, updateStatus, showCancelConfirmation, showAudioModeWarning, showDeleteAudioConfirm, imageGallery]);

  // Cleanup audio validation preview URL on unmount
  useEffect(() => {
    return () => {
      if (audioValidation?.previewUrl) {
        URL.revokeObjectURL(audioValidation.previewUrl);
      }
    };
  }, [audioValidation?.previewUrl]);

  // Handle touch events for swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd || !imageGallery) return;

    const minSwipeDistance = 50; // Minimum distance in pixels to be considered a swipe
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isLeftSwipe) {
      // Handle left swipe (next image)
      const nextIndex = (imageGallery.currentIndex + 1) % imageGallery.urls.length;
      setImageGallery({ ...imageGallery, currentIndex: nextIndex });
    }

    if (isRightSwipe) {
      // Handle right swipe (previous image)
      const prevIndex = (imageGallery.currentIndex - 1 + imageGallery.urls.length) % imageGallery.urls.length;
      setImageGallery({ ...imageGallery, currentIndex: prevIndex });
    }

    // Reset touch coordinates
    setTouchStart(null);
    setTouchEnd(null);
  };

  return (
    <div className={themeConfig.pageBackground}>
      {/* Background effects */}
      {themeConfig.backgroundEffects}

      {/* Theme-aware Header */}
      <header className="relative">
        <div className="container mx-auto px-3 py-3">
          <div
            className={`relative ${themeConfig.card} rounded-2xl px-4 py-3 border ${themeConfig.border} transition-all duration-300 overflow-hidden`}
            style={theme === 'dark' ? {
              boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
            } : {
              boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
            }}
          >
            {/* Content */}
            <div className="relative z-10 flex justify-center items-center">
              <LinkButton 
                href="/dashboard" 
                variant="ghost" 
                className="p-0 hover:bg-transparent active:scale-95"
              >
                <img
                  src={themeConfig.logo}
                  alt="Chhlat Bot"
                  className="h-8 w-auto transition-transform duration-300 hover:scale-105"
                />
              </LinkButton>
            </div>
          </div>
        </div>
      </header>

      <div className="flex-grow container mx-auto px-4 py-2">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Content header with title and back button */}
          <DashboardHeader 
            backHref="/dashboard/knowledge"
            titleKey="business_insight"
          />

          {/* View Modal */}
          <ViewModal
            viewingItem={viewingItem}
            questions={questions}
            onClose={() => setViewingItem(null)}
          />

          {/* Edit Modal */}
          <EditModal
            editingItem={editingItem}
            hasFocusedInput={hasFocusedInput}
            onValueChange={(value) => setEditingItem(prev => prev ? {...prev, value} : null)}
            onInputFocus={() => setHasFocusedInput(true)}
            onSave={handleSaveEdit}
            onClose={() => setEditingItem(null)}
          />

          {/* Update Confirmation Modal */}
          <UpdateConfirmationModal
            showConfirmation={showConfirmation}
            onCancel={() => setShowConfirmation(false)}
            onConfirm={saveQAToSupabase}
          />

          {/* Cancel Confirmation Modal */}
          <CancelConfirmationModal
            showCancelConfirmation={showCancelConfirmation}
            onKeepEditing={() => setShowCancelConfirmation(false)}
            onConfirmDiscard={confirmCancel}
          />

          {/* Delete Confirmation Modal */}
          <DeleteConfirmationModal
            deleteConfirm={deleteConfirm}
            isDeleting={isDeleting}
            onCancel={() => setDeleteConfirm(null)}
            onConfirmDelete={handleDeleteFromDatabase}
          />

          {/* Update Status Overlay */}
          <UpdateStatusOverlay
            updateStatus={updateStatus}
            updateProgress={updateProgress}
            updateMessage={updateMessage}
            onClose={() => setUpdateStatus('idle')}
          />

          {/* Recently Added Section - Only show when recentQA is set */}
          {recentQA && (
            <div
              className={`relative ${themeConfig.card} rounded-2xl p-6 mb-8 border ${themeConfig.border} transition-all duration-300 group overflow-visible`}
              style={theme === 'dark' ? {
                boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
              } : {
                boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
              }}
              ref={updateSectionRef}
            >

              <div className="relative z-10">
              <div className="flex flex-row justify-between items-center mb-6">
                <div className="flex-1">
                  <h2 className={`text-xl font-bold mb-1 font-title ${themeConfig.text}`}>{t('change_button')}</h2>
                  {/* <p className={`${themeConfig.textSecondary} text-sm font-body`}>
                    {t('change_business_insights')}
                  </p> */}
                </div>
                <div>
                  <Button
                    onClick={handleUpdate}
                    variant="primary"
                    size="md"
                    disabled={isUpdating || !recentQA || !hasRealChanges() || !hasValidContent()}
                    className={!hasRealChanges() || !hasValidContent() || isUpdating || !recentQA ? 'opacity-60' : ''}
                  >
                    {isUpdating ? t('changing') : t('change_button')}
                  </Button>
                </div>
              </div>

              {/* Help Section */}
              <HelpSection 
                youtubeUrl="#" // TODO: Replace with actual YouTube URL
                message={t('change_business_insights')}
                clientLang="en"
              />

              {/* List Items - Moved product search and selection to top */}
              <div className="w-full">
                <div className="space-y-4">

                  {/* Photo Search Bar */}
                  <PhotoSearchBar
                    searchQuery={photoSearchQuery}
                    onSearchChange={handlePhotoSearch}
                    searchResults={photoSearchResults}
                    isSearching={isSearching}
                    showResults={showPhotoResults}
                    onSelectPhoto={handleSelectPhoto}
                    onFocus={() => {
                      if (photoSearchResults.length > 0) {
                        setShowPhotoResults(true);
                      } else if (photoSearchQuery) {
                        searchPhotos(photoSearchQuery);
                      }
                    }}
                    placeholder={t('kb_search_photo')}
                  />

                  {/* Selected Photo Display */}
                  <SelectedPhotoDisplay
                    selectedPhoto={selectedPhoto}
                    isLoading={isPhotoLoading}
                    onRemovePhoto={handleClearSelectedPhoto}
                    onViewImage={handleViewImage}
                  />

                  {/* Question Field (View-only with modal) */}
                  <div className={`border ${themeConfig.border} rounded-lg p-4 mb-4`}>
                    <div className={`font-medium ${themeConfig.textSecondary} mb-2`}>{t('question')} <span className={`text-sm ${themeConfig.textMuted}`}>({t('kb_question_cannot_be_edited')})</span></div>
                    <div
                      className={`w-full px-3 py-2 ${themeConfig.secondCard} border ${themeConfig.border} rounded-lg ${themeConfig.text} ${themeConfig.borderHover} transition-colors cursor-pointer`}
                      onClick={() => handleViewItem('question', recentQA.question)}
                      title={t('click_to_view')}
                    >
                      <div className="truncate break-words">
                        {recentQA.question}
                      </div>
                    </div>
                  </div>


                  {/* Answer Edit Field */}
                  <div className={`border ${themeConfig.border} rounded-lg p-4`}>
                    <div className={`font-medium ${themeConfig.textSecondary} mb-2`}>{t('reply')}</div>
                    <AnswerEditField
                      value={recentQA.answer}
                      isAudioAnswer={isAudioAnswer}
                      audioUrl={recentQA.audio_url}
                      audioValidation={audioValidation}
                      isValidating={isValidatingAudio}
                      onClick={() => {
                        if (recentQA.audio_url) {
                          handleRecentlyAddedAudioClick(recentQA.id);
                        } else if (isAudioAnswer && audioValidation?.valid && audioValidation.previewUrl) {
                          handleValidatedAudioClick();
                        } else {
                          handleStartEdit(recentQA.id, 'answer', recentQA.answer);
                        }
                      }}
                      onAudioToggle={() => {
                        if (recentQA.audio_url) {
                          // Show confirmation before deleting audio file
                          setShowDeleteAudioConfirm(true);
                        } else if (isAudioAnswer && audioValidation?.valid) {
                          // Show confirmation before clearing validated audio code
                          setShowDeleteAudioConfirm(true);
                        } else if (isAudioAnswer) {
                          // Switch to text mode
                          setIsAudioAnswer(false);
                          if (audioValidation?.previewUrl) {
                            URL.revokeObjectURL(audioValidation.previewUrl);
                          }
                          setAudioValidation(null);
                        } else {
                          // Switch to audio mode
                          if (recentQA.answer?.trim()) {
                            setShowAudioModeWarning(true);
                          } else {
                            setIsAudioAnswer(true);
                          }
                        }
                      }}
                      audioId={recentQA.audio_url ? recentQA.id : 'validated-audio'}
                      isAudioSelected={recentQA.audio_url ?
                        recentlyAddedAudioId === recentQA.id :
                        recentlyAddedAudioId === 'validated-audio'}
                      isAudioPlaying={(recentQA.audio_url ?
                        recentlyAddedAudioId === recentQA.id :
                        recentlyAddedAudioId === 'validated-audio') && isRecentlyAddedAudioPlaying}
                      onAudioClick={() => recentQA.audio_url ?
                        handleRecentlyAddedAudioClick(recentQA.id) :
                        handleValidatedAudioClick()}
                      placeholder={t('enter_reply')}
                    />
                  </div>

                  {/* Cancel/Close Button */}
                  <div className="flex justify-end">
                    <Button
                      variant="danger"
                      size="md"
                      onClick={handleDelete}
                    >
                      {t('cancel')}
                    </Button>
                  </div>
                </div>
                </div>
              </div>
            </div>
          )}

          {/* Knowledge Base Section */}
          <div
            ref={(el) => {
              if (knowledgeBaseRef.current !== el) {
                (knowledgeBaseRef as any).current = el;
              }
              if (databaseSectionRef.current !== el) {
                (databaseSectionRef as any).current = el;
              }
            }}
            className={`relative ${themeConfig.card} rounded-2xl p-6 mb-8 border ${themeConfig.border} transition-all duration-300 group overflow-hidden`}
            style={theme === 'dark' ? {
              boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
            } : {
              boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
            }}
          >

            <div className="relative z-10">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
              <div>
                <h2 className={`text-xl font-bold mb-1 font-title ${themeConfig.text}`}>{t('kb_title')}</h2>
                <p className={`${themeConfig.textSecondary} text-sm font-body`}>
                  {t('kb_subtitle')}
                </p>
              </div>
              {/* Only show search when data is loaded */}
              {!isLoading && (
                <div className="mt-4 md:mt-0 w-full md:w-auto">
                  <div className="relative">
                    <input
                      type="text"
                      placeholder={t('kb_search_questions')}
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className={`w-full md:w-64 px-4 py-2 pl-10 ${themeConfig.secondCard} border ${themeConfig.border} rounded-lg ${themeConfig.text} focus:outline-none ${themeConfig.borderHover} transition-colors`}
                    />
                    <svg className={`w-5 h-5 ${themeConfig.textMuted} absolute left-3 top-2.5`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
              )}
            </div>

            {/* Loading State */}
            <LoadingState isLoading={isLoading} />

            {/* Error State */}
            {error && !isLoading && (
              <div className={`py-12 text-center ${themeConfig.errorText}`}>
                <p>{error}</p>
                <button
                  onClick={fetchQuestions}
                  className="mt-4 px-4 py-2 bg-zinc-800 hover:bg-zinc-700 rounded-lg text-white"
                >
                  {t('kb_try_again')}
                </button>
              </div>
            )}

            {/* Empty State */}
            {!isLoading && !error && filteredQuestions.length === 0 && (
              <div className={`py-12 text-center ${themeConfig.textMuted}`}>
                {searchQuery ? (
                  <p>{t('kb_no_questions_found')}</p>
                ) : (
                  <p>{t('kb_no_questions_added')}</p>
                )}
              </div>
            )}

            {/* Knowledge Base Content */}
            {!isLoading && !error && filteredQuestions.length > 0 && (
              <>
                {/* Knowledge Base Column Headers */}
                <div className={`flex border-b ${themeConfig.divider} py-3 font-semibold ${themeConfig.textSecondary} font-body`}>
                  <div className="w-[40%] md:w-[40%] md:px-2 text-left">{t('kb_question_column')}</div>
                  <div className="w-[40%] md:w-[40%] px-2 text-left">{t('kb_reply_column')}</div>
                  <div className="w-[12%] md:w-[15%] md:px-2 text-left"></div>
                  <div className="w-[8%] md:w-[5%] md:px-2 text-center"></div>
                </div>

                {/* FAQ List Container */}
                <div
                  ref={faqListRef}
                  className="w-full"
                >
                  {/* Render current page items */}
                  {currentPageData.map((qa, index) => {
                    const actualIndex = startIndex + index;
                      return (
                        <div
                          key={qa.id || `faq-${index}`}
                          className={`flex border-b ${themeConfig.divider} py-3 items-center`}
                        >
                      <div
                        className={`w-[40%] md:w-[40%] md:px-2 cursor-pointer rounded py-1 transition-colors`}
                        onClick={() => handleViewItem('question', qa.question_p || qa.question)}
                      >
                        <div className={`truncate break-words ${themeConfig.text}`} title={qa.question_p || qa.question}>
                          {qa.question_p || qa.question}
                        </div>
                      </div>
                      <div
                        className={`w-[40%] md:w-[40%] px-2 py-1 rounded transition-colors ${qa.audio_url ? 'flex justify-center items-center' : 'cursor-pointer'}`}
                        onClick={qa.audio_url ? undefined : () => handleViewItem('answer', qa.answer_p || qa.answer)}
                      >
                        {qa.audio_url ? (
                          <AudioPlayer
                            audioUrl={qa.audio_url}
                            compact={true}
                            isSelected={selectedAudioId === qa.id}
                            isPlaying={selectedAudioId === qa.id && isAudioPlaying}
                            onClick={() => handleAudioClick(qa.id)}
                            onError={handleAudioError}
                          />
                        ) : qa.is_audio_answer ? (
                          <div className="flex items-center">
                            <FaMicrophone className="w-3 h-3 mr-1 text-jade-purple flex-shrink-0" title="Audio answer" />
                            <span className={`text-xs ${themeConfig.textMuted} flex-shrink-0`}>
                              {t('voice')}
                            </span>
                          </div>
                        ) : (
                          <div className={`truncate break-words ${themeConfig.text}`} title={qa.answer_p || qa.answer}>
                            {qa.answer_p || qa.answer}
                          </div>
                        )}
                      </div>
                      <div className="w-[12%] md:w-[15%] px-1 md:px-2 py-1">
                        {qa.photo_url && (
                          <div className="h-full w-full px-1 md:px-3 py-1.5 rounded-lg flex items-center justify-center">
                            <PhotoThumbnail
                              photo={{
                                photo_url: extractImageUrls(qa.photo_url).allUrls || [],
                                photo_id: qa.photo_id || 'FAQ Image'
                              }}
                              className={`w-8 h-8 md:w-10 md:h-10 border ${themeConfig.border}`}
                              onClick={() => handleViewImage(qa.photo_url)}
                            />
                          </div>
                        )}
                      </div>
                      <div className="w-[8%] md:w-[5%] md:px-2 flex flex-col space-y-1">
                        <button
                          className="text-jade-purple hover:text-jade-purple-dark text-lg p-3"
                          onClick={() => handleMoveToRecentlyAdded(qa)}
                          title={t('kb_edit_question')}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </button>
                        <button
                          className="text-red-500 hover:text-red-400 text-lg p-3 transition-colors"
                          onClick={() => showDeleteConfirmation(qa.id)}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  )
                })}
                  </div>
                  
                  {/* Pagination Controls */}
                  <PaginationControls
                    currentPage={currentPage}
                    totalPages={totalPages}
                    itemsPerPage={itemsPerPage}
                    itemsPerPageOptions={itemsPerPageOptions}
                    startIndex={startIndex}
                    endIndex={endIndex}
                    totalItems={filteredQuestions.length}
                    onPageChange={goToPage}
                    onItemsPerPageChange={changeItemsPerPage}
                    onPreviousPage={goToPreviousPage}
                    onNextPage={goToNextPage}
                  />
              </>
            )}
            </div>
          </div>
        </motion.div>
      </div>

      <DashboardFooter />





      {/* Image Gallery Modal */}
      <ImageGalleryModal
        imageGallery={imageGallery}
        onClose={() => setImageGallery(null)}
        onImageChange={(index) => setImageGallery(imageGallery ? { ...imageGallery, currentIndex: index } : null)}
        onPrevious={showPreviousImage}
        onNext={showNextImage}
      />

      {/* Audio Mode Warning Modal */}
      <AudioModeWarningModal
        showAudioModeWarning={showAudioModeWarning}
        onCancel={() => setShowAudioModeWarning(false)}
        onConfirm={confirmAudioMode}
      />

      {/* Delete Audio Confirmation Modal */}
      <DeleteAudioConfirmationModal
        showDeleteAudioConfirm={showDeleteAudioConfirm}
        onCancel={() => setShowDeleteAudioConfirm(false)}
        onConfirmDelete={() => {
          if (recentQA?.audio_url) {
            // Delete existing audio file and switch to text mode
            setRecentQA({...recentQA, audio_url: undefined});
            setIsAudioAnswer(false);
            setOriginalAudioCode(null);
          } else {
            // Delete validated audio code
            handleConfirmAudioDelete();
            return; // handleConfirmAudioDelete already closes the modal
          }
          setShowDeleteAudioConfirm(false);
        }}
      />
    </div>
  )
}
