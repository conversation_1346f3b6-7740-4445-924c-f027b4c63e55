'use server'

import { cookies } from 'next/headers'
import { createServerClient } from '@supabase/ssr'

export interface AuthResult {
  authenticated: boolean
  authId: string | null
  clientId: string | null
}

/**
 * Authentication verification using Supabase session only
 * Extracts auth_id from session for use in API calls
 */
export async function verifyAuth(): Promise<AuthResult> {
  try {
    // Get Supabase auth session to extract auth_id
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            // Not needed for this context
          },
          remove(name: string, options: any) {
            // Not needed for this context
          },
        },
      }
    )

    // Get user from Supabase auth session
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user?.id) {
      return {
        authenticated: false,
        authId: null,
        clientId: null
      }
    }

    // Return auth result with auth_id and client_id
    return {
      authenticated: true,
      authId: user.id,
      clientId: user.app_metadata?.client_id || null
    }

  } catch (error) {
    console.error('Error in verifyAuth:', error)
    return {
      authenticated: false,
      authId: null,
      clientId: null
    }
  }
}


