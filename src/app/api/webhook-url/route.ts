import { NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { generateWebhookToken } from '@/utils/jwt'

export async function POST(request: Request) {
  try {
    const { webhookId } = await request.json()

    // Verify authentication
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized' 
      }, { status: 401 })
    }

    // Validate client ID
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }

    // Get the secure webhook URL from environment
    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
    
    if (!webhookUrl) {
      console.warn('Secure webhook URL not configured')
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Database webhook not configured'
      }, { status: 500 })
    }

    // Generate JWT token for webhook authentication
    const jwtToken = generateWebhookToken()

    // Get client credentials to retrieve webhook URLs
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${jwtToken}`
      },
      body: JSON.stringify({
        operation: 'SELECT',
        sql: `SELECT fb_url, ig_url, wa_url, tg_url, web_url 
              FROM client_credentials 
              WHERE client_id = $1`,
        params: [clientId]
      })
    })

    if (!response.ok) {
      throw new Error(`Webhook request failed: ${response.status} ${response.statusText}`)
    }

    let webhookData = await response.json()
    
    // Handle array response format from N8N webhook
    if (Array.isArray(webhookData) && webhookData.length > 0) {
      webhookData = webhookData[0]
    }
    
    if (!webhookData.success) {
      throw new Error(webhookData.error_msg || 'Database operation failed')
    }

    const credentialsData = webhookData.body || []
    const credentials = credentialsData && credentialsData.length > 0 ? credentialsData[0] : null

    // Handle missing credentials for webhook URLs (not for privacy policy)
    if (!credentials && webhookId !== 'privacy-policy') {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'No connection credentials found. Please contact support.'
      }, { status: 404 })
    }

    // Return the appropriate webhook URL
    let platformWebhookUrl = '';

    if (webhookId === 'privacy-policy') {
      platformWebhookUrl = 'https://www.chhlatbot.com/privacy';
    } else if (webhookId === 'fb-webhook') {
      platformWebhookUrl = credentials?.fb_url || 'https://api.chhlatbot.com/webhook/messenger';
    } else if (webhookId === 'ig-webhook') {
      platformWebhookUrl = credentials?.ig_url || 'https://api.chhlatbot.com/webhook/instagram';
    } else if (webhookId === 'wa-webhook') {
      platformWebhookUrl = credentials?.wa_url || 'https://api.chhlatbot.com/webhook/whatsapp';
    } else if (webhookId === 'web-webhook') {
      platformWebhookUrl = credentials?.web_url || 'https://api.chhlatbot.com/webhook/web';
    } else {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Invalid webhook ID'
      }, { status: 400 })
    }

    return NextResponse.json({
      success: true,
      body: { url: platformWebhookUrl },
      error_msg: null
    })

  } catch (error) {
    console.error('Error in webhook-url API:', error)
    return NextResponse.json({
      success: false,
      body: null,
      error_msg: error instanceof Error ? error.message : 'Internal server error'
    }, { status: 500 })
  }
}
