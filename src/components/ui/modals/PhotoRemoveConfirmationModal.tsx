'use client'

import { useRef, useEffect } from 'react'
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'
import { Button } from '@/components/ui'

interface PhotoRemoveConfirmationModalProps {
  showRemovePhotoConfirm: boolean
  onCancel: () => void
  onConfirm: () => void
}

export default function PhotoRemoveConfirmationModal({ 
  showRemovePhotoConfirm, 
  onCancel, 
  onConfirm 
}: PhotoRemoveConfirmationModalProps) {
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()
  const removePhotoConfirmRef = useRef<HTMLDivElement>(null)

  // Handle escape key to cancel
  useEffect(() => {
    if (showRemovePhotoConfirm) {
      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === 'Escape') {
          event.preventDefault()
          onCancel()
        }
      }
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [showRemovePhotoConfirm, onCancel])

  if (!showRemovePhotoConfirm) return null

  return (
    <div
      className={`fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50`}
    >
      <div
        ref={removePhotoConfirmRef}
        className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-red-900/[0.8] border-red-500/50'}`}
        onClick={(e: React.MouseEvent) => e.stopPropagation()}
      >
        {theme === 'dark' && (
          <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-transparent opacity-50 rounded-2xl"></div>
        )}
        <div className="relative z-10">
          <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.text}`}>
            {t('remove_photo_title')}
          </h3>
          <p className={`${themeConfig.textSecondary} mb-6 text-center`}>
            {t('remove_photo_message')}
          </p>
          <div className="flex space-x-3">
            <Button
              onClick={onCancel}
              variant="cancel"
              size="md"
              className="flex-1"
            >
              {t('cancel')}
            </Button>
            <Button
              onClick={onConfirm}
              variant="danger"
              size="md"
              className="flex-1"
            >
              {t('yes_remove')}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}