















- help me examine how the knowledge page handle knowledge cache for its frontend and pass to it api, and how its api use it (also the fallback machanism for these cache data that pass, and update once finish to server and frontend back)

- then check knowledgebase page to see how it handle and pass those cache data to its api (if the api needed (so check which CRUD that need it and pass accordingly), so we can eliminate some cache called again inside its api), and its cache fall mechansim to match knowledge page flow and logic. keep in mind that we only interested in cache handling here, so leave those postgres or webhook interaction untouch.