'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import { FaFacebook, FaInstagram, FaTiktok, FaTelegram, FaYoutube } from 'react-icons/fa'
import { useLanguage } from '@/context/LanguageContext'

export default function FooterLight() {
  const currentYear = new Date().getFullYear()
  const { t } = useLanguage()

  const footerLinks = [
    {
      titleKey: 'footer_product',
      links: [
        { nameKey: 'footer_features', href: '#features' },
        { nameKey: 'footer_pricing', href: '#pricing' },
        { nameKey: 'footer_how_it_works', href: '#how-it-works' },
        { nameKey: 'footer_faq', href: '#faq' },
      ]
    },
    {
      titleKey: 'footer_company',
      links: [
        { nameKey: 'footer_about', href: '#' },
        { nameKey: 'footer_blog', href: '#' },
        { nameKey: 'footer_careers', href: '#' },
        { nameKey: 'footer_contact', href: '#' },
      ]
    },
  ]

  const socialLinks = [
    { name: 'Facebook', icon: <FaFacebook className="text-lg" />, href: '#' },
    { name: 'TikTok', icon: <FaTiktok className="text-lg" />, href: '#' },
    { name: 'Instagram', icon: <FaInstagram className="text-lg" />, href: '#' },
    { name: 'YouTube', icon: <FaYoutube className="text-lg" />, href: '#' },
    { name: 'Telegram', icon: <FaTelegram className="text-lg" />, href: '#' },
  ]

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <footer className="relative overflow-hidden mt-20">
      {/* Enhanced modern clean footer - Light Mode */}
      <div className="container mx-auto px-4 py-16 relative z-10">
        <div className="relative bg-white rounded-2xl p-10 border border-gray-300 overflow-hidden">

          {/* Enhanced Content */}
          <div className="relative z-10">
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-10">
              <div className="col-span-1 md:col-span-2">
                <div
                  className="mb-8 cursor-pointer group"
                  onClick={scrollToTop}
                >
                  <Image
                    src="/images/purple_tran_logo.svg"
                    alt="Chhlat Bot"
                    width={140}
                    height={45}
                    className="h-12 w-auto transition-all duration-300 group-hover:scale-105 drop-shadow-sm"
                  />
                </div>
                <p className="font-body mb-10 text-gray-600 text-base leading-relaxed max-w-md">
                  {t('footer_description')}
                </p>
                <div className="flex space-x-4">
                  {socialLinks.map((social, index) => (
                    <motion.a
                      key={index}
                      href={social.href}
                      className="w-12 h-12 bg-gray-50 border border-gray-200 rounded-2xl flex items-center justify-center text-gray-600 hover:text-jade-purple-dark hover:bg-jade-purple/10 hover:border-jade-purple/30 hover:shadow-lg hover:shadow-jade-purple/10 transition-all duration-300"
                      whileHover={{ y: -3, scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                      aria-label={social.name}
                    >
                      {social.icon}
                    </motion.a>
                  ))}
                </div>
              </div>

              {footerLinks.map((section, index) => (
                <div key={index}>
                  <h4 className="text-lg font-bold mb-6 font-title text-gray-900">{t(section.titleKey)}</h4>
                  <ul className="space-y-3">
                    {section.links.map((link, linkIndex) => (
                      <li key={linkIndex}>
                        <a
                          href={link.href}
                          className="text-gray-600 hover:text-jade-purple-dark transition-all duration-300 font-body text-sm hover:translate-x-1 inline-block transform hover:font-medium"
                        >
                          {t(link.nameKey)}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>

            {/* Enhanced bottom section */}
            <div className="mt-16 pt-8 border-t border-gray-200">
              <div className="flex flex-col md:flex-row justify-between items-center space-y-6 md:space-y-0">
                <p className="text-gray-600 text-sm font-body">
                  &copy; {currentYear} <span className="text-jade-purple-dark font-bold">ChhlatBot</span> {t('footer_rights')}
                </p>
                <div className="flex flex-wrap justify-center gap-8 text-sm font-body">
                  <a href="#" className="text-gray-600 hover:text-jade-purple-dark transition-all duration-200 hover:font-medium">
                    {t('footer_privacy')}
                  </a>
                  <a href="#" className="text-gray-600 hover:text-jade-purple-dark transition-all duration-200 hover:font-medium">
                    {t('footer_terms')}
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
} 