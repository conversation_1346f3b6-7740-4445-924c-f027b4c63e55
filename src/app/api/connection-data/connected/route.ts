import { NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { generateWebhookToken } from '@/utils/jwt'

// Define connected platform type
type ConnectedPlatform = {
  platform: string
  displayName: string
  status: number
  connectionType?: string // For telegram bot/business distinction
}

export async function GET() {
  try {
    // Authentication check
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized' 
      }, { status: 401 })
    }

    // Validate client ID
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }

    // Get the secure webhook URL from environment
    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
    
    if (!webhookUrl) {
      console.warn('Secure webhook URL not configured')
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Database webhook not configured'
      }, { status: 500 })
    }

    // Generate JWT token for webhook authentication
    const jwtToken = generateWebhookToken()

    // Simple query for connected platforms only
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${jwtToken}`
      },
      body: JSON.stringify({
        operation: 'SELECT',
        sql: `SELECT fb_name, ig_name, tg_name, web_name, web_domain, 
                     fb_status, ig_status, tg_status, web_status, tg_id_name
              FROM client_credentials 
              WHERE client_id = $1`,
        params: [clientId]
      })
    })

    if (!response.ok) {
      throw new Error(`Webhook request failed: ${response.status} ${response.statusText}`)
    }

    let webhookData = await response.json()
    
    // Handle N8N array wrapper
    if (Array.isArray(webhookData) && webhookData.length > 0) {
      webhookData = webhookData[0]
    }
    
    if (!webhookData.success) {
      throw new Error(webhookData.error_msg || 'Database operation failed')
    }

    const credentialsData = webhookData.body || []

    // If no credentials record exists, return empty array
    if (!credentialsData || credentialsData.length === 0) {
      return NextResponse.json({
        success: true,
        body: { connectedPlatforms: [] },
        error_msg: null
      })
    }

    const data = credentialsData[0]
    const connectedPlatforms: ConnectedPlatform[] = []

    // Simple filter: only add platforms that have names
    if (data.fb_name) {
      connectedPlatforms.push({
        platform: 'facebook',
        displayName: data.fb_name,
        status: data.fb_status || 1
      })
    }

    if (data.ig_name) {
      connectedPlatforms.push({
        platform: 'instagram', 
        displayName: data.ig_name,
        status: data.ig_status || 1
      })
    }

    if (data.tg_name) {
      connectedPlatforms.push({
        platform: 'telegram',
        displayName: data.tg_name,
        connectionType: data.tg_id_name?.includes('bot') ? 'bot' : 'business',
        status: data.tg_status || 1
      })
    }

    if (data.web_name || data.web_domain) {
      connectedPlatforms.push({
        platform: 'web',
        displayName: data.web_domain || data.web_name,
        status: data.web_status || 1
      })
    }

    return NextResponse.json({
      success: true,
      body: { connectedPlatforms },
      error_msg: null
    })

  } catch (error) {
    console.error('Error in connected platforms API:', error)
    return NextResponse.json({
      success: false,
      body: null,
      error_msg: error instanceof Error ? error.message : 'Internal server error'
    }, { status: 500 })
  }
}