'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'

// Define the language type
export type Language = 'km' | 'en'

// Define the context type
interface LanguageContextType {
  language: Language
  setLanguage: (lang: Language) => void
  t: (key: string) => string
}

// Create the context
const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

// Browser language detection helper
const detectBrowserLanguage = (): Language => {
  if (typeof window === 'undefined') return 'en'
  
  const browserLang = navigator.language.toLowerCase()
  if (browserLang.startsWith('km')) return 'km'
  if (browserLang.startsWith('en')) return 'en'
  
  // Default to English for international audience
  return 'en'
}

// Create the provider component
export function LanguageProvider({ children }: { children: ReactNode }) {
  // Default to browser-detected language
  const [language, setLanguageState] = useState<Language>(() => detectBrowserLanguage())
  
  // State for dynamically loaded translations
  const [translations, setTranslations] = useState<Record<string, string>>({})
  const [isLoading, setIsLoading] = useState(true)

  // Function to load translations dynamically
  const loadTranslations = async (lang: Language) => {
    try {
      setIsLoading(true)
      const translationModule = await import(`../locales/${lang}.json`)
      setTranslations(translationModule.default || translationModule)
      setIsLoading(false)
    } catch (error) {
      console.error(`Error loading translations for language: ${lang}`, error)
      // Fallback to English if current language fails to load
      if (lang !== 'en') {
        try {
          const fallbackModule = await import('../locales/en.json')
          setTranslations(fallbackModule.default || fallbackModule)
        } catch (fallbackError) {
          console.error('Error loading fallback English translations:', fallbackError)
          setTranslations({}) // Empty object as last resort
        }
      } else {
        setTranslations({}) // Empty object as last resort
      }
      setIsLoading(false)
    }
  }

  // Load translations when language changes
  useEffect(() => {
    loadTranslations(language)
  }, [language])

  // Load language preference from localStorage or cookie on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        // Try to get language from localStorage first (highest priority)
        let savedLang = localStorage.getItem('uiLanguage') as Language

        // If not in localStorage, try to get from cookie as fallback
        if (!savedLang || (savedLang !== 'km' && savedLang !== 'en')) {
          const cookies = document.cookie.split(';')
          const langCookie = cookies.find(cookie => cookie.trim().startsWith('uiLanguage='))
          if (langCookie) {
            savedLang = langCookie.split('=')[1].trim() as Language
          }
        }

        // If we have a saved preference, use it
        if (savedLang && (savedLang === 'km' || savedLang === 'en')) {
          setLanguageState(savedLang)
          document.documentElement.lang = savedLang
        } else {
          // No saved preference - detect browser language for new users
          const detectedLang = detectBrowserLanguage()
          setLanguageState(detectedLang)
          document.documentElement.lang = detectedLang
        }
      } catch (error) {
        console.error('Error loading language preference:', error)
        // Default to Khmer in case of error
        document.documentElement.lang = 'km'
      }
    }
  }, [])

  // Handle language change
  const setLanguage = (lang: Language) => {
    setLanguageState(lang)
    if (typeof window !== 'undefined') {
      try {
        // Set the language in localStorage
        localStorage.setItem('uiLanguage', lang)

        // Update HTML lang attribute
        document.documentElement.lang = lang

        // Also set a cookie as a fallback for browsers with localStorage issues
        document.cookie = `uiLanguage=${lang}; path=/; max-age=31536000; SameSite=Lax` // 1 year expiry

        // console.log('Language set in context:', lang)
      } catch (error) {
        console.error('Error setting language:', error)
      }
    }
  }

  // Translation function with dynamic loading support
  const t = (key: string): string => {
    // If still loading, return the key to avoid blank content
    if (isLoading) {
      return key
    }
    
    // Return translation or fallback to key
    return translations[key] || key
  }

  const value = {
    language,
    setLanguage,
    t
  }

  return <LanguageContext.Provider value={value}>{children}</LanguageContext.Provider>
}

// Hook to use the language context
export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}

