'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useAuth } from '@/context/AuthContext'
import { useLanguage } from '@/context/LanguageContext'
import Link from 'next/link'
import Image from 'next/image'
import { FaPhone } from 'react-icons/fa'
import LanguageSwitcher from './LanguageSwitcher'

export default function Navbar() {
  const [scrolled, setScrolled] = useState(false)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const { user, signOut } = useAuth()
  const { t } = useLanguage()

  useEffect(() => {
    let ticking = false;
    
    const handleScroll = () => {
      if (!ticking) {
        window.requestAnimationFrame(() => {
          const isScrolled = window.scrollY > 10;
          if (isScrolled !== scrolled) {
            setScrolled(isScrolled);
          }
          ticking = false;
        });
        ticking = true;
      }
    };

    // Use passive scroll for better performance
    document.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      document.removeEventListener('scroll', handleScroll);
    };
  }, [scrolled]);

  // No longer prevent scrolling when mobile menu is open
  // Removed the scroll prevention effect to allow normal scrolling

  const navLinks = [
    { title: 'features', target: '#features' },
    { title: 'pricing', target: '#pricing' },
    { title: 'how_it_works', target: '#how-it-works' },
    { title: 'faq', target: '#faq' }
  ]

  const scrollToSection = (target: string) => {
    setMobileMenuOpen(false)

    // Get the target element
    const element = document.querySelector(target)
    if (!element) return

    // Small delay to ensure mobile menu closes before scrolling
    setTimeout(() => {
      // Get window width for responsive offset
      const isMobile = window.innerWidth < 768
      const offset = isMobile ? 140 : 80 // Much larger offset for mobile

      // Scroll to the element
      window.scrollTo({
        top: element.getBoundingClientRect().top + window.scrollY - offset,
        behavior: 'smooth'
      })
    }, 100)
  }

  const scrollToTop = () => {
    setMobileMenuOpen(false)
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const handleSignOut = async () => {
    try {
      await signOut()
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  // The regular navbar that shows when menu is closed
  const renderNavContent = () => (
    <div className="flex justify-between items-center">
      {/* Phone Icon - Visible on mobile and iPad */}
      <div className="lg:hidden">
        <a href="tg://resolve?domain=chhlatbot" className="w-8 h-8 flex items-center justify-center bg-white rounded-full text-jade-purple-dark">
          <FaPhone size={14} />
        </a>
      </div>

      {/* Logo - Centered on mobile, left-aligned on desktop */}
      <div className="flex-1 md:flex-initial flex justify-center md:justify-start">
        <button onClick={scrollToTop} className="relative">
          <div className="relative w-[120px] h-10">
            <Image
              src="/images/white_tran_logo.svg"
              alt="Chhlat Bot"
              fill
              style={{ objectFit: 'contain' }}
              sizes="120px"
              priority
            />
          </div>
        </button>
      </div>

      {/* Desktop Menu */}
      <div className="hidden lg:flex items-center space-x-8">
        {navLinks.map((link, index) => (
          <button
            key={index}
            onClick={() => scrollToSection(link.target)}
            className="text-gray-300 hover:text-white font-body transition-colors"
          >
            {t(link.title)}
          </button>
        ))}

        {user ? (
          <div className="flex items-center space-x-4">
            <LanguageSwitcher />
            <Link
              href="/dashboard"
              className="btn-glass-filled-flat"
            >
              {t('dashboard')}
            </Link>
          </div>
        ) : (
          <div className="flex items-center space-x-4">
            <LanguageSwitcher />
            <Link
              href="/access"
              className="btn-glass-filled-flat"
            >
              {t('login')}
            </Link>
          </div>
        )}
      </div>

      {/* Mobile Menu Button */}
      <div className="flex lg:hidden items-center">
        {/* Menu Button */}

        {/* Mobile/Tablet Menu Button */}
        <div className="z-50">
          <motion.button
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="text-white p-2 transition-colors duration-300"
            aria-label="Toggle menu"
            whileTap={{ scale: 0.95 }}
          >
            <motion.div
              animate={{ rotate: mobileMenuOpen ? 90 : 0 }}
              transition={{ duration: 0.2, ease: "easeOut" }}
            >
              {mobileMenuOpen ? (
                <motion.svg
                  width="24"
                  height="24"
                  fill="none"
                  viewBox="0 0 24 24"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.2 }}
                >
                  <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </motion.svg>
              ) : (
                <motion.svg
                  width="24"
                  height="24"
                  fill="none"
                  viewBox="0 0 24 24"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.2 }}
                >
                  <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
                </motion.svg>
              )}
            </motion.div>
          </motion.button>
        </div>
      </div>
    </div>
  );

  return (
    <nav className="fixed top-0 w-full z-50">
      <div className="py-3">
        <div className="container mx-auto px-4 md:px-6">
          <motion.div
            className="relative bg-gradient-to-br from-white/[0.08] to-white/[0.02] rounded-2xl border border-white/20 overflow-hidden"
            style={{
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(255, 255, 255, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
              // Use transform for better performance
              transform: 'translateZ(0)',
              // Only apply backdrop-filter on devices that support it well
              backdropFilter: 'blur(16px)',
              WebkitBackdropFilter: 'blur(16px)'
            }}
            animate={{
              borderRadius: mobileMenuOpen ? "1rem" : "1rem"
            }}
            transition={{ 
              duration: 0.2, 
              ease: "easeOut",
              // Only animate the border radius
              filter: { duration: 0 }
            }}
          >
            {/* Subtle background gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-jade-purple/5 to-transparent opacity-30 rounded-2xl"></div>

            {/* Main Navigation Content */}
            <div className="relative z-10 px-4 py-3">
              {renderNavContent()}
            </div>

            {/* Expandable Mobile Menu */}
            <AnimatePresence>
              {mobileMenuOpen && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: "auto", opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ 
                    duration: 0.2, 
                    ease: "easeOut",
                    opacity: { duration: 0.15 }
                  }}
                  className="overflow-hidden border-t border-white/20 will-change-[height,opacity]"
                >
                  <div className="relative z-10 px-6 py-4">
                    {/* Navigation Links */}
                    {navLinks.map((link, index) => (
                      <motion.button
                        key={index}
                        onClick={() => {
                          scrollToSection(link.target)
                          setMobileMenuOpen(false)
                        }}
                        className="w-full text-center py-4 text-white font-title text-lg hover:bg-white/10 rounded-xl will-change-transform transition-colors duration-200"
                        initial={{ opacity: 0, y: -5 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -5 }}
                        transition={{ 
                          duration: 0.15, 
                          ease: "easeOut",
                          delay: index * 0.03 
                        }}
                      >
                        {t(link.title)}
                      </motion.button>
                    ))}

                    {/* Divider */}
                    <div className="pt-2 mt-2">
                      <div className="flex flex-col space-y-4">
                        {/* Language Switcher */}
                        <motion.div
                          className="flex justify-center px-4 py-2 will-change-transform"
                          initial={{ opacity: 0, y: -5 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -5 }}
                          transition={{ 
                            duration: 0.15,
                            ease: "easeOut"
                          }}
                        >
                          <LanguageSwitcher />
                        </motion.div>

                        {/* Action Button */}
                        <motion.div
                          initial={{ opacity: 0, y: -5 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -5 }}
                          transition={{ 
                            duration: 0.15,
                            ease: "easeOut"
                          }}
                          className="flex justify-center will-change-transform"
                        >
                          {user ? (
                            <Link
                              href="/dashboard"
                              className="px-6 py-3 bg-jade-purple hover:bg-jade-purple/80 rounded-xl text-white text-center font-medium transition-all duration-300"
                              onClick={() => setMobileMenuOpen(false)}
                            >
                              {t('dashboard')}
                            </Link>
                          ) : (
                            <Link
                              href="/access"
                              className="px-6 py-3 bg-jade-purple hover:bg-jade-purple/80 rounded-xl text-white text-center font-medium transition-all duration-300"
                              onClick={() => setMobileMenuOpen(false)}
                            >
                              {t('login')}
                            </Link>
                          )}
                        </motion.div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
      </div>
    </nav>
  )
}