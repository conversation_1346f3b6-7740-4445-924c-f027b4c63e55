# Smart Commit Instruction

When the user types `/commit` or references this file, perform the following automated git workflow:

## Steps to Execute:

1. **Check Status**: First run `git status` to see what files have changed
2. **Stage Changes**: Run `git add .` to stage all changes
3. **Analyze Changes**: Look at the staged files and their modifications
4. **Generate Message**: Create an appropriate commit message based on:
   - File types modified (components, styles, docs, config, etc.)
   - Nature of changes (new features, bug fixes, updates, refactoring)
   - Number of files changed
   - Follow conventional commit format when possible

## IMPORTANT: Commit Message Format
- Generate ONLY a single-line commit message
- Do NOT add any Claude Code attribution
- Do NOT add co-author information  
- Do NOT add footers or metadata
- Use ONLY this format: `git commit -m "type: description"`

## Commit Message Guidelines:

- **Single file changes**: 
  - `feat: add user authentication component`
  - `fix: resolve login validation issue`
  - `style: update header component styling`
  - `docs: update README installation steps`

- **Multiple file changes**:
  - `feat: implement user dashboard with navigation`
  - `refactor: restructure component architecture`
  - `update: enhance UI components and styling`

- **File type patterns**:
  - `.js/.jsx/.ts/.tsx` → `feat:` or `fix:`
  - `.css/.scss/.sass` → `style:`
  - `.md` → `docs:`
  - `.json/.config` → `config:`
  - `package.json` → `deps:` (for dependencies)


## Example Workflow:

```bash
# Check what's changed
git status

# Stage all changes
git add .

# Commit with appropriate message
git commit -m "feat: add user profile component with validation"


## Rules:
  - Always stage ALL changes with `git add .`
  - Generate descriptive, concise commit messages
  - Use present tense ("add" not "added")
  - Keep messages under 72 characters when possible
  - If changes span multiple areas, use a general description
