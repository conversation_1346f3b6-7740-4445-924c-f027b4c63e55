CREATE TABLE public.model_pricing (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  model TEXT NOT NULL,
  provider TEXT NOT NULL,
  key TEXT NOT NULL,
  input_cost_per_token DECIMAL(12,9),
  output_cost_per_token DECIMAL(12,9),
  cache_read_cost_per_token DECIMAL(12,9) DEFAULT 0,
  cache_write_cost_per_token DECIMAL(12,9) DEFAULT 0,
  thinking_cost_per_token DECIMAL(12,9) DEFAULT 0,
  audio_input_cost_per_token DECIMAL(12,9) DEFAULT 0,
  updated_at TIMESTAMP WITH TIME ZONE NULL DEFAULT NOW()
);

-- Add trigger for auto-updating updated_at
CREATE TRIGGER trg_update_model_pricing_timestamp 
  BEFORE UPDATE ON public.model_pricing 
  FOR EACH ROW 
  EXECUTE FUNCTION public.update_modified_column();