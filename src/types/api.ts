// Standard API Response Interface
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
  cached?: boolean  // For cached responses
  executionTime?: number  // For SQL execution timing
}

// Paginated Response Interface
export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  total_pages: number
}

// Webhook Response Interface (for normalization)
export interface WebhookResponse {
  body?: any
  data?: any
  error_msg?: string
  [key: string]: any
}