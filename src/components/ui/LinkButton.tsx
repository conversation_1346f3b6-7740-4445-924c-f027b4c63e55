import Link, { LinkProps } from 'next/link';
import { twMerge } from 'tailwind-merge';
import { ButtonVariant, ButtonSize } from './Button';
import { useThemeConfig } from '@/context/ThemeContext';

interface LinkButtonProps extends Omit<LinkProps, 'href'>, Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, keyof LinkProps> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  fullWidth?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  isActive?: boolean;
  href: string;
}

const LinkButton = ({
  children,
  href,
  variant = 'primary',
  size = 'md',
  className = '',
  fullWidth = false,
  leftIcon,
  rightIcon,
  isActive = false,
  ...props
}: LinkButtonProps) => {
  const themeConfig = useThemeConfig();
  
  const baseStyles = 'inline-flex items-center justify-center rounded-lg font-body font-medium focus:outline-none focus:ring-0 focus-visible:outline-none focus-visible:ring-0 outline-none transition-all duration-200 active:scale-[0.98] no-underline';
  
  const variants = {
    primary: `bg-jade-purple-dark text-white border border-jade-purple hover:bg-jade-purple ${
      isActive ? 'bg-jade-purple-dark' : ''
    }`,
    secondary: `${themeConfig.secondCard} ${themeConfig.text} border ${themeConfig.border} ${themeConfig.secondCardHover} ${
      isActive ? themeConfig.interactive : ''
    }`,
    ghost: `bg-transparent ${themeConfig.text} hover:${themeConfig.interactive} border border-transparent ${
      isActive ? themeConfig.interactive : ''
    }`,
    outline: `bg-transparent ${themeConfig.text} border border-jade-purple/60 hover:border-jade-purple/80 hover:bg-jade-purple/10 ${
      isActive ? 'bg-jade-purple/20' : ''
    }`,
    danger: 'bg-red-800/90 text-white border border-red-700 hover:bg-red-700/90',
    success: 'bg-green-800/80 text-white border border-green-600/50 hover:bg-green-700/90',
    cancel: `${themeConfig.secondCard} ${themeConfig.text} border ${themeConfig.border} ${themeConfig.secondCardHover} ${isActive ? themeConfig.interactive : ''}`,
  };

  const sizes = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
    icon: 'p-2', // Square icon button
  };

  return (
    <Link
      href={href}
      className={twMerge(
        baseStyles,
        variants[variant],
        sizes[size],
        fullWidth && 'w-full',
        // Only apply hover styles on devices that support hover (not touch devices)
        'max-md:hover:opacity-100', // Reset hover opacity on mobile
        'max-md:hover:shadow-none',  // Reset hover shadow on mobile
        // Apply hover styles only on hover-supported devices
        // 'md:hover:opacity-90 md:hover:shadow-md',
        // Active states for all devices
        // 'active:opacity-80 active:shadow-inner',
        // Remove any browser default focus styles
        '!outline-none !ring-0 focus:!outline-none focus:!ring-0 focus-visible:!outline-none focus-visible:!ring-0',
        className
      )}
      {...props}
    >
      {leftIcon && <span className="mr-2">{leftIcon}</span>}
      {children}
      {rightIcon && <span className="ml-2">{rightIcon}</span>}
    </Link>
  );
};

export { LinkButton };
