import { MetadataRoute } from 'next'

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: 'ChhlatBot - Automated Customer Replies on Social Media',
    short_name: 'ChhlatBot',
    description: 'ChhlatBot provides an automated reply system for social media, handling 90% of repetitive customer inquiries with AI across Facebook, Instagram, Telegram, and Website. It can understand text and audio messages in over 100 languages, including Khmer, offering enterprise-level customer service automation for small and medium-sized businesses at minimal cost. (TikTok integration coming soon!)',
    start_url: '/',
    display: 'standalone',
    background_color: '#1e293b',
    theme_color: '#866bff',
    icons: [
      {
        src: '/images/chhlat_500.png',
        sizes: '500x500',
        type: 'image/png',
      },
      {
        src: '/images/jade_128.png',
        sizes: '128x128',
        type: 'image/png',
      },
      {
        src: '/images/jade_64_b.png',
        sizes: '64x64',
        type: 'image/png',
      },
    ],
    categories: ['business', 'social', 'productivity'],
    lang: 'km',
    orientation: 'portrait-primary',
    scope: '/',
  }
}