// Telegram bot utilities for audio file operations

export class TelegramAudioClient {
  private botToken: string

  constructor() {
    this.botToken = process.env.TELEGRAM_AUDIO_BOT_TOKEN!
    
    if (!this.botToken) {
      throw new Error('TELEGRAM_AUDIO_BOT_TOKEN environment variable is required')
    }
  }

  private get baseUrl() {
    return `https://api.telegram.org/bot${this.botToken}`
  }

  async getFile(fileId: string) {
    try {
      const response = await fetch(`${this.baseUrl}/getFile?file_id=${fileId}`)
      
      if (!response.ok) {
        throw new Error(`Telegram API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      
      if (!data.ok) {
        throw new Error(`Telegram API error: ${data.description}`)
      }

      return data.result
    } catch (error) {
      console.error('Error getting file from Telegram:', error)
      throw error
    }
  }

  async downloadFile(filePath: string): Promise<Buffer> {
    try {
      const downloadUrl = `https://api.telegram.org/file/bot${this.botToken}/${filePath}`
      const response = await fetch(downloadUrl)
      
      if (!response.ok) {
        throw new Error(`Failed to download file: ${response.status} ${response.statusText}`)
      }

      const arrayBuffer = await response.arrayBuffer()
      return Buffer.from(arrayBuffer)
    } catch (error) {
      console.error('Error downloading file from Telegram:', error)
      throw error
    }
  }

  async downloadAudioFile(fileId: string): Promise<{ buffer: Buffer, mimeType: string }> {
    try {
      // First get file info
      const fileInfo = await this.getFile(fileId)
      
      if (!fileInfo.file_path) {
        throw new Error('File path not found in Telegram response')
      }

      // Download the file
      const buffer = await this.downloadFile(fileInfo.file_path)
      
      // Determine mime type from file extension
      let mimeType = 'audio/ogg' // Default
      const extension = fileInfo.file_path.split('.').pop()?.toLowerCase()
      
      switch (extension) {
        case 'mp3':
          mimeType = 'audio/mpeg'
          break
        case 'mp4':
        case 'm4a':
          mimeType = 'audio/mp4'
          break
        case 'ogg':
          mimeType = 'audio/ogg'
          break
        case 'wav':
          mimeType = 'audio/wav'
          break
        case 'webm':
          mimeType = 'audio/webm'
          break
      }

      return { buffer, mimeType }
    } catch (error) {
      console.error('Error downloading audio file:', error)
      throw error
    }
  }
}

// Export singleton instance
export const telegramAudio = new TelegramAudioClient()