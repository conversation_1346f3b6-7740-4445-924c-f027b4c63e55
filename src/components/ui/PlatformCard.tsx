'use client'

import { ReactNode } from 'react'
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'
import { Button } from '@/components/ui'

interface PlatformCardProps {
  platform: string
  name: string
  icon: ReactNode
  displayName: string
  isConnected: boolean
  isEnabled: boolean
  onToggle: (platform: string) => void
  backgroundColor?: string
  // New props for disconnect functionality
  mode?: 'toggle' | 'disconnect'
  onDisconnect?: (platform: string) => void
  isDeleting?: boolean
}

export default function PlatformCard({
  platform,
  name,
  icon,
  displayName,
  isConnected,
  isEnabled,
  onToggle,
  backgroundColor,
  mode = 'toggle',
  onDisconnect,
  isDeleting = false
}: PlatformCardProps) {
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()

  if (!isConnected) return null

  const cardBg = backgroundColor || themeConfig.card

  return (
    <div className={`${cardBg} border ${themeConfig.border} ${themeConfig.borderHover} rounded-lg p-4 flex items-center justify-between transition-all`}>
      <div className="flex items-center justify-between w-full">
        <div className="flex items-center">
          <div className={`w-10 h-10 ${themeConfig.secondCard} rounded-full flex items-center justify-center mr-4 border ${themeConfig.border}`}>
            {icon}
          </div>
          <div>
            <h3 className={`font-medium ${themeConfig.text} text-base font-title`}>{name}</h3>
            <p className={`${themeConfig.textSecondary} text-xs font-body truncate max-w-[150px]`}>
              {t('name')}: {displayName || t('not_set')}
            </p>
          </div>
        </div>
        <div className="flex items-center">
          {mode === 'toggle' ? (
            <div className="relative inline-block w-12 h-6 transition duration-200 ease-in-out">
              <input
                type="checkbox"
                id={`toggle-${platform}`}
                className="absolute w-6 h-6 opacity-0 z-10 cursor-pointer"
                checked={isEnabled}
                onChange={() => onToggle(platform)}
              />
              <label
                htmlFor={`toggle-${platform}`}
                className={`block h-6 overflow-hidden rounded-full cursor-pointer transition-colors duration-200 ${
                  isEnabled ? 'bg-green-500/30' : 'bg-gray-700'
                }`}
              >
                <span
                  className={`block h-6 w-6 rounded-full transform transition-all duration-200 ease-in-out ${
                    isEnabled ? 'bg-green-500 translate-x-6 scale-105' : 'bg-white -translate-x-0.5'
                  }`}
                />
              </label>
            </div>
          ) : (
            <Button
              onClick={() => onDisconnect?.(platform)}
              disabled={isDeleting}
              variant="danger"
              size="sm"
              className="px-4 py-2"
            >
              {isDeleting ? t('disconnecting') : t('disconnect')}
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}