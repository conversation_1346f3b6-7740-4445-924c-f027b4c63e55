'use client'

import { useEffect } from 'react'
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'
import { Button } from '@/components/ui'

interface DeleteAudioConfirmationModalProps {
  showDeleteAudioConfirm: boolean
  onCancel: () => void
  onConfirmDelete: () => void
}

export default function DeleteAudioConfirmationModal({ 
  showDeleteAudioConfirm, 
  onCancel, 
  onConfirmDelete 
}: DeleteAudioConfirmationModalProps) {
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()

  // Handle escape key to cancel
  useEffect(() => {
    if (showDeleteAudioConfirm) {
      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === 'Escape') {
          event.preventDefault()
          onCancel()
        }
      }
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [showDeleteAudioConfirm, onCancel])

  if (!showDeleteAudioConfirm) return null

  return (
    <div
      className={`fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50`}
    >
      <div
        className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-red-900/[0.8] border-red-500/50'}`}
        onClick={(e: React.MouseEvent) => e.stopPropagation()}
      >
        {theme === 'dark' && (
          <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-transparent opacity-50 rounded-2xl"></div>
        )}
        <div className="relative z-10">
          <div className="flex items-center justify-center mb-4">
            <div className={`p-3 rounded-full ${theme === 'light' ? 'bg-red-100' : 'bg-red-500/20'}`}>
              <svg className="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </div>
          </div>
          <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.text}`}>
            {t('delete_audio_file')}
          </h3>
          <p className={`${themeConfig.textSecondary} mb-6 text-center`}>
            {t('delete_audio_warning')}
          </p>
          <div className="flex space-x-3">
            <Button
              onClick={onCancel}
              variant="cancel"
              size="md"
              className="flex-1"
            >
              {t('cancel')}
            </Button>
            <Button
              onClick={onConfirmDelete}
              variant="danger"
              size="md"
              className="flex-1"
            >
              {t('delete')}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}