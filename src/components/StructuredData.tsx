'use client'

import { useLanguage } from '@/context/LanguageContext'
import { getSEOTranslations } from '@/lib/seo-translations'

export default function StructuredData() {
  const { language } = useLanguage()
  const translations = getSEOTranslations(language)
  
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://chhlat.com'
  
  // Organization Schema with Cambodia-specific data
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": translations.organizationName,
    "alternateName": "ChhlatBot",
    "url": baseUrl,
    "logo": {
      "@type": "ImageObject",
      "url": `${baseUrl}/images/chhlat_500.png`,
      "width": "500",
      "height": "500"
    },
    "description": translations.organizationDescription,
    "foundingLocation": {
      "@type": "Place",
      "name": "Cambodia",
      "address": {
        "@type": "PostalAddress",
        "addressCountry": "KH"
      }
    },
    "areaServed": {
      "@type": "Place",
      "name": "Cambodia"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": translations.businessTerms.customerService,
      "availableLanguage": ["km", "en"]
    },
    "knowsLanguage": [
      {
        "@type": "Language",
        "name": "Khmer",
        "alternateName": "km"
      },
      {
        "@type": "Language", 
        "name": "English",
        "alternateName": "en"
      }
    ]
  }
  
  // Software Application Schema with detailed features
  const softwareSchema = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": translations.softwareName,
    "alternateName": "ChhlatBot",
    "url": baseUrl,
    "description": translations.softwareDescription,
    "applicationCategory": "BusinessApplication",
    "applicationSubCategory": "Social Media Management",
    "operatingSystem": "Web",
    "browserRequirements": "Modern web browser",
    "softwareVersion": "1.0",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "description": translations.freeTrialDescription,
      "availableAtOrFrom": {
        "@type": "Place",
        "name": "Cambodia"
      }
    },
    "featureList": [
      translations.features.automatedResponses,
      translations.features.khmerSupport,
      translations.features.multiMedia,
      translations.features.analytics
    ],
    "supportedDevice": ["Desktop", "Mobile", "Tablet"],
    "inLanguage": ["km", "en"],
    "creator": {
      "@type": "Organization",
      "name": translations.organizationName,
      "url": baseUrl
    },
    "provider": {
      "@type": "Organization",
      "name": translations.organizationName,
      "url": baseUrl
    }
  }
  
  // Website Schema with proper structure
  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": translations.softwareName,
    "alternateName": "ChhlatBot",
    "url": baseUrl,
    "description": translations.websiteDescription,
    "inLanguage": language,
    "isAccessibleForFree": true,
    "audience": {
      "@type": "Audience",
      "audienceType": "Business",
      "geographicArea": {
        "@type": "Place",
        "name": "Cambodia"
      }
    },
    "about": {
      "@type": "Thing",
      "name": translations.businessTerms.socialMediaManagement,
      "description": translations.softwareDescription
    },
    "publisher": {
      "@type": "Organization",
      "name": translations.organizationName,
      "url": baseUrl
    }
  }
  
  // Local Business Schema for Cambodia presence
  const localBusinessSchema = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": translations.organizationName,
    "description": translations.organizationDescription,
    "url": baseUrl,
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "KH"
    },
    "areaServed": {
      "@type": "Place",
      "name": "Cambodia"
    },
    "serviceArea": {
      "@type": "Place",
      "name": "Cambodia"
    },
    "knowsLanguage": ["km", "en"],
    "paymentAccepted": "USD",
    "priceRange": "Free Trial"
  }
  
  const schemas = [
    organizationSchema,
    softwareSchema,
    websiteSchema,
    localBusinessSchema
  ]
  
  return (
    <>
      {schemas.map((schema, index) => (
        <script
          key={index}
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(schema)
          }}
        />
      ))}
    </>
  )
}