import { NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { audioRedis } from '@/utils/redis'
import { telegramAudio } from '@/utils/telegram'
import { convertToMP4A, validateAudioBuffer } from '@/utils/audioConversion'

export async function POST(request: Request) {
  try {
    // Verify authentication
    const { authenticated, authId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // Parse request body
    const { audioCode } = await request.json()

    if (!audioCode || typeof audioCode !== 'string') {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Missing required field: audioCode'
      }, { status: 400 })
    }

    // Check Redis for audio data
    const audioData = await audioRedis.getAudioData(audioCode)

    if (!audioData) {
      return NextResponse.json({
        success: true,
        body: {
          valid: false,
          duration: 0,
          fileSize: 0,
          timestamp: null,
          alreadyUsed: false
        },
        error_msg: null
      })
    }

    // If audio data exists, process the audio file and return blob
    try {
      // Download audio from Telegram
      const { buffer, mimeType } = await telegramAudio.downloadAudioFile(audioData.file_id)
      
      // Validate audio buffer
      const validation = validateAudioBuffer(buffer, mimeType)
      if (!validation.valid) {
        return NextResponse.json({
          success: false,
          body: null,
          error_msg: `Invalid audio data: ${validation.error}`
        }, { status: 400 })
      }
      
      // Convert audio to MP4A AAC format
      const conversionResult = await convertToMP4A(buffer, mimeType)
      
      // Return processed audio as blob in response
      return new Response(conversionResult.buffer, {
        status: 200,
        headers: {
          'Content-Type': 'audio/x-m4a',
          'X-Audio-Valid': 'true',
          'X-Audio-Duration': String(audioData.duration || 0),
          'X-Audio-FileId': audioData.file_id,
          'X-Audio-FileSize': String(audioData.file_size || 0),
          'X-Audio-Timestamp': audioData.time || '',
          'X-Audio-AlreadyUsed': String(audioData.process || false)
        }
      })
      
    } catch (error) {
      console.error('Audio processing error:', error)
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: error instanceof Error ? error.message : 'Audio processing failed'
      }, { status: 500 })
    }

  } catch (error: unknown) {
    console.error('Error in audio validation API:', error)
    return NextResponse.json({
      success: false,
      body: null,
      error_msg: error instanceof Error ? error.message : 'Internal server error'
    }, { status: 500 })
  }
}