'use client'

import { useThemeConfig } from '@/context/ThemeContext'

export default function ThemeAwareSkeleton() {
  const themeConfig = useThemeConfig()
  
  return (
    <div className="space-y-6">
      {/* Stats Cards Loading */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {[...Array(3)].map((_, index) => (
          <div 
            key={index}
            className={`${themeConfig.skeletonCard} rounded-xl p-4 animate-pulse`}
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <div className={`h-8 ${themeConfig.skeletonElement} rounded w-1/3 mb-4`}></div>
            <div className={`h-6 ${themeConfig.skeletonElement} rounded w-2/3`}></div>
          </div>
        ))}
      </div>
      
      {/* Features Grid Loading */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {[...Array(6)].map((_, index) => (
          <div 
            key={index}
            className={`${themeConfig.skeletonCard} rounded-xl p-6 animate-pulse`}
            style={{ animationDelay: `${index * 0.15}s` }}
          >
            <div className={`w-12 h-12 ${themeConfig.skeletonElement} rounded-full mb-4`}></div>
            <div className={`h-6 ${themeConfig.skeletonElement} rounded w-2/3 mb-3`}></div>
            <div className={`h-4 ${themeConfig.skeletonElement} rounded w-full`}></div>
          </div>
        ))}
      </div>
    </div>
  )
}