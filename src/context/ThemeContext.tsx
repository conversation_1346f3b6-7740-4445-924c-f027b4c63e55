'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'

// Define the theme type
export type Theme = 'dark' | 'light'

// Define the context type
interface ThemeContextType {
  theme: Theme
  setTheme: (theme: Theme) => void
  toggleTheme: () => void
}

// Create the context
const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

// Define theme configuration
export const themes = {
  dark: {
    // Main backgrounds
    bg: 'bg-dashboard-dark-bg',
    pageBackground: 'min-h-screen flex flex-col relative bg-dashboard-dark-bg',
    
    // Background effects
    backgroundEffects: (
      <>
        <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-white/5 rounded-full blur-[150px] -z-10"></div>
        <div className="absolute bottom-1/3 right-1/3 w-1/3 h-1/3 bg-dashboard-primary/5 rounded-full blur-[120px] -z-10"></div>
      </>
    ),
    
    // Universal backgrounds - transparent with shadowbox effect
    card: '',
    // card: 'bg-white/[0.075]',
    secondCard: 'bg-dashboard-dark-surface',
    secondCardHover: 'hover:bg-dashboard-dark-surface-dark',
    interactive: 'bg-dashboard-dark-interactive',
    interactiveDark: 'bg-dashboard-dark-surface-dark',
    interactiveHover: 'md:hover:bg-dashboard-dark-interactive-hover',
    
    // Universal borders
    border: 'border-dashboard-dark-border-default',
    borderHover: 'md:hover:border-dashboard-dark-border-hover',
    borderActive: 'focus:border-dashboard-dark-border-hover',
    cardBorder: 'border-dashboard-dark-border-default',
    cardBackground: 'bg-dashboard-dark-surface',
    
    // Universal text colors - #F9FAFB, #D1D5DB, #9CA3AF
    text: 'text-dashboard-dark-text-primary',        // #F9FAFB - light primary text
    textSecondary: 'text-dashboard-dark-text-secondary', // #D1D5DB - secondary text
    textMuted: 'text-dashboard-dark-text-muted',     // #9CA3AF - muted text
    
    // Universal hover states
    hover: 'hover:text-dashboard-dark-text-primary hover:bg-jade-purple-dark hover:border-dashboard-dark-border-hover',
    
    // Logo
    logo: '/images/white_tran_logo.svg',
    
    // Progress bar
    progressBackground: 'bg-dashboard-dark-border-muted',
    
    // Circular stats
    statCircleBackground: 'rgba(21, 19, 30, 0.8)',
    statCircleBorder: 'rgba(68, 67, 71, 0.5)', 
    statCircleTrack: 'rgba(85, 85, 85, 0.3)',
    
    // Loading skeleton
    skeletonCard: 'bg-dashboard-dark-surface border-dashboard-dark-border-default',
    skeletonElement: 'bg-dashboard-dark-surface-dark',
    
    // Error/Warning styles
    errorBackground: 'bg-dashboard-error/10',
    errorBorder: 'border-dashboard-error/30',
    errorText: 'text-dashboard-error',
    errorShadow: 'shadow-lg shadow-dashboard-error/10',
    
    warningBackground: 'bg-dashboard-warning/10',
    warningBorder: 'border-dashboard-warning/30',
    warningText: 'text-dashboard-warning',
    warningShadow: 'shadow-lg shadow-dashboard-warning/10',
    
    usageWarningBackground: 'bg-dashboard-warning/10',
    usageWarningBorder: 'border-dashboard-warning/30',
    usageWarningText: 'text-dashboard-warning',
    usageWarningShadow: 'shadow-lg shadow-dashboard-warning/10',
    
    // Divider
    divider: 'border-dashboard-dark-border-muted',
    
    // Component reference
    footerComponent: 'Footer'
  },
  light: {
    // Main backgrounds
    bg: 'bg-dashboard-light-bg',
    pageBackground: 'min-h-screen flex flex-col relative bg-dashboard-light-bg',
    
    // Background effects (none for light theme)
    backgroundEffects: null,
    
    // Universal backgrounds
    card: 'bg-dashboard-light-surface',
    secondCard: 'bg-dashboard-light-surface-dark',
    secondCardHover: 'hover:bg-dashboard-light-interactive-hover',
    interactive: 'bg-dashboard-light-interactive-hover',
    interactiveDark: 'bg-dashboard-light-interactive',
    interactiveHover: 'md:hover:bg-jade-purple-light',
    
    // Universal borders
    border: 'border-dashboard-light-border-default',
    borderHover: 'md:hover:border-dashboard-light-border-hover',
    borderActive: 'focus:border-dashboard-light-border-active',
    cardBorder: 'border-dashboard-light-border-default',
    cardBackground: 'bg-dashboard-light-surface',
    
    // Universal text colors - #111827, #374151, #6B7280
    text: 'text-dashboard-light-text-primary',        // #111827 - dark primary text
    textSecondary: 'text-dashboard-light-text-secondary', // #374151 - secondary text
    textMuted: 'text-dashboard-light-text-muted',     // #6B7280 - muted text
    
    // Universal hover states
    hover: 'hover:bg-jade-purple-light hover:border-dashboard-light-border-hover',
    
    // Logo
    logo: '/images/purple_tran_logo.svg',
    
    // Progress bar
    progressBackground: 'bg-dashboard-light-border-muted',
    
    // Circular stats
    statCircleBackground: '#F5F7FB',
    statCircleBorder: 'rgba(229, 231, 235, 0.8)',
    statCircleTrack: 'rgba(229, 231, 235, 0.5)',
    
    // Loading skeleton  
    skeletonCard: 'bg-dashboard-light-surface border-dashboard-light-border-default',
    skeletonElement: 'bg-dashboard-light-bg',
    
    // Error/Warning styles
    errorBackground: 'bg-dashboard-light-surface',
    errorBorder: 'border-dashboard-error-light',
    errorText: 'text-dashboard-error-light',
    errorShadow: '',
    
    warningBackground: 'bg-dashboard-light-surface',
    warningBorder: 'border-dashboard-warning-light',
    warningText: 'text-dashboard-warning-light',
    warningShadow: '',
    
    usageWarningBackground: 'bg-dashboard-light-surface',
    usageWarningBorder: 'border-dashboard-warning-light',
    usageWarningText: 'text-dashboard-warning-light',
    usageWarningShadow: '',
    
    // Divider
    divider: 'border-dashboard-light-border-muted',
    
    // Component reference
    footerComponent: 'Footer'
  }
}

// Create the provider component
export function ThemeProvider({ children }: { children: ReactNode }) {
  // Default to dark theme
  const [theme, setThemeState] = useState<Theme>('dark')

  // Load theme preference from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        // Get the initial theme from the HTML element class (set by the script in layout)
        const htmlEl = document.documentElement;
        const currentTheme = Array.from(htmlEl.classList).find(cls => 
          cls.endsWith('-theme')
        )?.replace('-theme', '');

        // Try to get theme from localStorage first
        let savedTheme = localStorage.getItem('uiTheme') as Theme

        // If not in localStorage, use the theme from HTML class
        if (!savedTheme || (savedTheme !== 'dark' && savedTheme !== 'light')) {
          savedTheme = (currentTheme === 'light' ? 'light' : 'dark') as Theme;
        }

        // Set theme state if valid
        const themeToApply = (savedTheme && (savedTheme === 'dark' || savedTheme === 'light')) ? savedTheme : 'dark'
        setThemeState(themeToApply)
        
        // Apply theme class to html element
        htmlEl.className = htmlEl.className.replace(/\b(light|dark)(-theme)?\b/g, '');
        htmlEl.classList.add(`${themeToApply}-theme`);
      } catch (error) {
        console.error('Error loading theme preference:', error)
      }
    }
  }, [])

  // Handle theme change
  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme)
    if (typeof window !== 'undefined') {
      try {
        // Set the theme in localStorage
        localStorage.setItem('uiTheme', newTheme)

        // Also set a cookie as a fallback for browsers with localStorage issues
        document.cookie = `uiTheme=${newTheme}; path=/; max-age=31536000; SameSite=Lax` // 1 year expiry

        // Apply theme class to html element
        const htmlEl = document.documentElement;
        htmlEl.className = htmlEl.className.replace(/\b(light|dark)(-theme)?\b/g, '');
        htmlEl.classList.add(`${newTheme}-theme`);
      } catch (error) {
        console.error('Error setting theme:', error)
      }
    }
  }

  // Toggle between themes
  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark')
  }

  const value = {
    theme,
    setTheme,
    toggleTheme
  }

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
}

// Hook to use the theme context
export function useTheme() {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

// Helper hook to get current theme configuration
export function useThemeConfig() {
  const { theme } = useTheme()
  return themes[theme]
}
