import { NextResponse } from 'next/server';
import { verifyAuth } from '@/utils/auth';
import { generateWebhookToken } from '@/utils/jwt';
import { serverCache } from '@/lib/cache';


export async function POST(request: Request) {
  try {
    // Verify authentication
    const { authenticated, authId, clientId } = await verifyAuth();
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized' 
      }, { status: 401 });
    }

    // Validate client ID
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 });
    }

    // Parse request body
    const body = await request.json();
    const { platform, status } = body;

    // Validate required parameters
    if (!platform || typeof status !== 'boolean') {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Missing required parameters: platform (string), status (boolean)'
      }, { status: 400 });
    }

    // Validate platform
    const validPlatforms = ['facebook', 'instagram', 'telegram', 'web'];
    if (!validPlatforms.includes(platform)) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: `Invalid platform. Must be one of: ${validPlatforms.join(', ')}`
      }, { status: 400 });
    }

    // Get the secure webhook URL from environment
    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
    
    if (!webhookUrl) {
      console.warn('Secure webhook URL not configured')
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Database webhook not configured'
      }, { status: 500 })
    }

    // Generate JWT token for webhook authentication
    const jwtToken = generateWebhookToken()

    // Try to get plan_type from dashboard cache first
    const dashboardCacheKey = `dashboard_${authId}`
    const cachedDashboardData = serverCache.get(dashboardCacheKey)
    let planType = 'free' // Default fallback

    if (cachedDashboardData?.clientInfo?.plan_type) {
      planType = cachedDashboardData.clientInfo.plan_type
    } else {
    }


    // If trying to enable, perform validation checks
    if (status === true) {
      let clientData: any = null;

      // 1. Check if we have plan_type from cache, only fetch client data if needed
      if (cachedDashboardData?.clientInfo?.plan_type) {
        // We have plan info from cache, only fetch client billing/usage data
        const clientResponse = await fetch(webhookUrl, {
          method: 'POST',
          headers: { 
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${jwtToken}`
          },
          body: JSON.stringify({
            operation: 'SELECT',
            sql: `SELECT next_billing_date, usage_used, usage_limit
                  FROM clients 
                  WHERE client_id = $1`,
            params: [clientId]
          })
        })

        if (!clientResponse.ok) {
          throw new Error(`Client webhook request failed: ${clientResponse.status} ${clientResponse.statusText}`)
        }

        let clientWebhookData = await clientResponse.json()
        
        // Handle N8N array wrapper
        if (Array.isArray(clientWebhookData) && clientWebhookData.length > 0) {
          clientWebhookData = clientWebhookData[0]
        }
        
        if (!clientWebhookData.success) {
          throw new Error(clientWebhookData.error_msg || 'Client data operation failed')
        }

        const clientResult = clientWebhookData.body || []

        if (!clientResult || clientResult.length === 0) {
          // No client data found - use defaults for free plan
          clientData = { 
            plan_type: planType,
            next_billing_date: null,
            usage_used: 0,
            usage_limit: 2000
          };
        } else {
          clientData = { ...clientResult[0], plan_type: planType };
        }
      } else {
        // No cache, fetch full client data including plan_type
        const clientResponse = await fetch(webhookUrl, {
          method: 'POST',
          headers: { 
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${jwtToken}`
          },
          body: JSON.stringify({
            operation: 'SELECT',
            sql: `SELECT plan_type, next_billing_date, usage_used, usage_limit
                  FROM clients 
                  WHERE client_id = $1`,
            params: [clientId]
          })
        })

        if (!clientResponse.ok) {
          throw new Error(`Client webhook request failed: ${clientResponse.status} ${clientResponse.statusText}`)
        }

        let clientWebhookData = await clientResponse.json()
        
        // Handle N8N array wrapper
        if (Array.isArray(clientWebhookData) && clientWebhookData.length > 0) {
          clientWebhookData = clientWebhookData[0]
        }
        
        if (!clientWebhookData.success) {
          throw new Error(clientWebhookData.error_msg || 'Client data operation failed')
        }

        const clientResult = clientWebhookData.body || []

        if (!clientResult || clientResult.length === 0) {
          // No client data found - use defaults for free plan
          clientData = { 
            plan_type: 'free',
            next_billing_date: null,
            usage_used: 0,
            usage_limit: 2000
          };
          planType = 'free';
        } else {
          clientData = clientResult[0];
          planType = clientData.plan_type || 'free';
        }
      }

      // Check if billing is overdue
      if (clientData.next_billing_date) {
        const billingDate = new Date(clientData.next_billing_date);
        const today = new Date();
        const todayInPhnomPenh = new Date(today.toLocaleString('en-US', { timeZone: 'Asia/Phnom_Penh' }));
        todayInPhnomPenh.setHours(0, 0, 0, 0);

        if (billingDate < todayInPhnomPenh) {
          return NextResponse.json({
            success: false,
            body: null,
            error_msg: 'Cannot enable platform: Your plan has expired. Please renew your subscription.'
          }, { status: 403 });
        }
      }

      // 2. Check usage limits (usage data is now in clients table)
      const usageUsed = clientData.usage_used || 0;
      const usageLimit = clientData.usage_limit || 2000;

      if (usageUsed >= usageLimit) {
        return NextResponse.json({
          success: false,
          body: null,
          error_msg: 'Cannot enable platform: Usage limit reached. Please upgrade your plan.'
        }, { status: 403 });
      }

    }


    // Send platform toggle information to secure webhook
    const webhookResponse = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${jwtToken}`
      },
      body: JSON.stringify({
        operation: 'toggle',
        mode: 'connection',
        client_id: clientId,
        platform: platform,
        status: status ? 1 : 0,
        sql: `SELECT fb_url, ig_url, tg_url, web_url, tg_biz_id FROM client_credentials WHERE client_id = $1`,
        params: [clientId]
      }),
    });

    if (!webhookResponse.ok) {
      throw new Error(`Toggle webhook request failed: ${webhookResponse.status} ${webhookResponse.statusText}`)
    }

    let webhookData = await webhookResponse.json()
    
    // Handle N8N array wrapper
    if (Array.isArray(webhookData) && webhookData.length > 0) {
      webhookData = webhookData[0]
    }
    
    if (!webhookData.success) {
      return NextResponse.json({
        success: false,
        body: webhookData.body || null,
        error_msg: webhookData.error_msg || 'Toggle operation failed'
      }, { status: 400 });
    }


    return NextResponse.json({
      success: true,
      body: {
        message: `${platform} status updated successfully`,
        status: status
      },
      error_msg: null
    });

  } catch (error) {
    console.error('Error in platform toggle API:', error);
    return NextResponse.json({
      success: false,
      body: null,
      error_msg: error instanceof Error ? error.message : 'Internal server error'
    }, { status: 500 });
  }
}
