'use client'

import { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import Image from 'next/image';
import { useRouter, usePathname } from 'next/navigation';
import { useKnowledgeData } from '@/hooks/useOptimizedData';
import { FaBrain, FaImage, FaMicrophone } from 'react-icons/fa';
import { useLanguage } from '@/context/LanguageContext';
import { useTheme, useThemeConfig } from '@/context/ThemeContext';
import { optimizeGalleryImage } from '@/utils/imageOptimization';
import { motion } from 'framer-motion';
import { Button, LinkButton, DashboardHeader } from '@/components/ui'; // Import our new Button components
import LanguageSwitcher from '@/components/LanguageSwitcher';
import DashboardFooter from '@/components/DashboardFooter';
import AudioPlayer from '@/components/ui/AudioPlayer';
import { 
  ViewModal, 
  EditModal, 
  UpdateConfirmationModal, 
  DeleteConfirmationModal, 
  AudioModeWarningModal, 
  DeleteAudioConfirmationModal,
  ImageGalleryModal 
} from '@/components/ui/modals';
import KnowledgeTopSection from '@/components/ui/knowledge/KnowledgeTopSection';
import HelpSection from '@/components/ui/knowledge/HelpSection';
import { PhotoSearchBar, SelectedPhotoDisplay, UpdateStatusOverlay } from '@/components/ui/knowledge';

// Memoized dummy data for knowledge base questions
const useDummyQuestions = () => {
  return useMemo(() =>
    Array.from({ length: 90 }, (_, i) => ({
      id: i + 1,
      question: `How does your product ${i % 3 === 0 ? 'handle customer data security' : i % 3 === 1 ? 'compare to competitors' : 'benefit my business'}? ${i % 5 === 0 ? 'I need detailed information about the features, benefits, and limitations.' : ''}`,
      answer: `Our product ${i % 3 === 0 ? 'uses end-to-end encryption to secure all customer data and complies with GDPR and other privacy regulations' : i % 3 === 1 ? 'offers 24/7 support, better pricing, and more features than competitors in the same category' : 'increases efficiency by 40% and reduces operational costs significantly'}. ${i % 4 === 0 ? 'We also provide comprehensive documentation and training materials to help you get the most out of our services.' : ''}`,
      dateAdded: new Date(2025, 3, Math.floor(Math.random() * 30) + 1).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' }),
      isActive: Math.random() > 0.2
    })),
    [] // Empty dependency array ensures data is only created once
  );
}

// Define the type for uploaded PDFs including the File object
// type UploadedPdf = {
//   id: number;
//   name: string;
//   size: string;
//   date: string;
//   file: File; // Add the File object
// };

// Define the type for photo info in QA items
interface PhotoInfo {
  id: number;
  photo_id: string;
  photo_url: string | null; // Single thumbnail for display
  full_photo_urls: string[] | null; // Complete array of photos
}

// Define the type for editing item
interface EditItem {
  id: number;
  field: 'question' | 'answer';
  value: string;
}

// Optimized PhotoThumbnail component for fast loading without Vercel Image
const PhotoThumbnail = ({
  photo,
  className = "w-10 h-10",
  onClick
}: {
  photo: { photo_url: string[] | null, photo_id: string },
  className?: string,
  onClick?: () => void
}) => {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)
  const themeConfig = useThemeConfig()

  return (
    <div
      className={`${className} ${themeConfig.skeletonElement} rounded overflow-hidden flex-shrink-0 relative ${onClick ? 'cursor-pointer' : ''}`}
      onClick={onClick}
    >
      {photo.photo_url && photo.photo_url.length > 0 && !imageError ? (
        <>
          {/* Loading placeholder - no animation */}
          <div className={`absolute inset-0 ${themeConfig.skeletonElement} transition-opacity duration-200 ${imageLoaded ? 'opacity-0' : 'opacity-100'}`} />

          {/* Actual image */}
          <img
            src={photo.photo_url[0]}
            alt={photo.photo_id}
            className={`w-full h-full object-cover transition-opacity duration-200 ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
            loading="lazy"
            decoding="async"
            onLoad={() => setImageLoaded(true)}
            onError={() => {
              setImageError(true)
              setImageLoaded(true)
            }}
          />
        </>
      ) : (
        <div className={`w-full h-full flex items-center justify-center ${themeConfig.skeletonElement} ${themeConfig.textMuted} text-xs`}>
          <svg width="24" height="18" viewBox="0 0 24 18" fill="currentColor">
            <rect width="24" height="18" rx="2" fill="currentColor" opacity="0.3"/>
            <path d="M3 13L7 9L11 13L15 6L21 12V15H3V13Z" fill="currentColor" opacity="0.6"/>
            <circle cx="7" cy="6" r="2" fill="currentColor" opacity="0.6"/>
          </svg>
        </div>
      )}
    </div>
  )
}

export default function KnowledgePage() {
  const router = useRouter()
  const pathname = usePathname()
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()

  // Use unified knowledge cache for all knowledge page data
  const { 
    data: knowledgeData, 
    loading: isKnowledgeLoading,
    addPhotoToState,
    updatePhotoInState,
    removePhotoFromState,
    updateFaqCountInState
  } = useKnowledgeData()
  const clientLang = knowledgeData?.clientLang
  const knowledgeStats = knowledgeData?.knowledgeStats

  const [isUploading, setIsUploading] = useState(false)
  const [question, setQuestion] = useState<string>('')
  const [answer, setAnswer] = useState<string>('')
  const [isAudioAnswer, setIsAudioAnswer] = useState<boolean>(false)
  const [isValidatingAudio, setIsValidatingAudio] = useState<boolean>(false)
  const [audioValidation, setAudioValidation] = useState<{valid: boolean, duration?: number, file_id?: string, previewUrl?: string, error?: string} | null>(null)
  const [recentQA, setRecentQA] = useState<Array<{
    id: number,
    question: string,
    answer: string,
    isAudioAnswer?: boolean,
    audioDuration?: number,
    audioFileId?: string,
    processedAudioBlob?: Blob,
    photoInfo?: PhotoInfo
  }>>([])
  const [isUpdating, setIsUpdating] = useState(false)
  const [editingItem, setEditingItem] = useState<EditItem | null>(null)
  const [hasFocusedInput, setHasFocusedInput] = useState<boolean>(false)
  const [showConfirmation, setShowConfirmation] = useState(false)
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false)
  const [itemToDelete, setItemToDelete] = useState<number | null>(null)
  const [updateStatus, setUpdateStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  const [updateMessage, setUpdateMessage] = useState('')
  const [updateProgress, setUpdateProgress] = useState(0)
  const [showAudioModeWarning, setShowAudioModeWarning] = useState(false)
  const [showAudioDeleteConfirm, setShowAudioDeleteConfirm] = useState(false)
  
  // Audio playback state for preview section
  const [selectedPreviewAudioId, setSelectedPreviewAudioId] = useState<string | null>(null)
  const [isPreviewAudioPlaying, setIsPreviewAudioPlaying] = useState(false)
  
  // Audio playback state for Recently Added section
  const [recentQAPreviewUrls, setRecentQAPreviewUrls] = useState<Map<number, string>>(new Map())
  const [selectedRecentAudioId, setSelectedRecentAudioId] = useState<number | null>(null)
  const [isRecentAudioPlaying, setIsRecentAudioPlaying] = useState(false)

  // Get knowledge stats from dashboard cache instead of separate state
  const totalFaqs = knowledgeStats?.faqCount || 0
  const totalFaqsLimit = knowledgeStats?.faqLimit || 0
  const faqUsagePercentage = knowledgeStats?.faqUsagePercentage || 0
  const photoCount = knowledgeStats?.photoCount || 0
  const photoLimit = knowledgeStats?.photoLimit || 0
  const photoUsagePercentage = knowledgeStats?.photoUsagePercentage || 0
  const isLoadingCount = isKnowledgeLoading

  // Add new state for photo search
  const [photoSearchQuery, setPhotoSearchQuery] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const [photoSearchResults, setPhotoSearchResults] = useState<Array<{
    id: number,
    photo_id: string,
    photo_url: string[] | null,
    photo_file_path: string[] | null
  }>>([])
  const [selectedPhoto, setSelectedPhoto] = useState<{
    id: number,
    photo_id: string,
    photo_url: string | null,
    full_photo_urls: string[] | null
  } | null>(null)
  const [isPhotoLoading, setIsPhotoLoading] = useState(false)
  const [showPhotoResults, setShowPhotoResults] = useState(false)
  const searchResultsRef = useRef<HTMLDivElement>(null)

  const questionInputRef = useRef<HTMLInputElement>(null)
  const answerInputRef = useRef<HTMLInputElement>(null)

  // Viewing popup state
  const [viewingItem, setViewingItem] = useState<{field: 'question' | 'answer', value: string} | null>(null)

  // Image gallery state
  const [imageGallery, setImageGallery] = useState<{urls: string[], currentIndex: number} | null>(null)
  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)

  // Remove PDF upload state
  // const [uploadedPdfs, setUploadedPdfs] = useState<Array<{id: number, name: string, size: string, date: string, file: File}>>([]);
  // const fileInputRef = useRef<HTMLInputElement>(null)
  // const [dragActive, setDragActive] = useState(false)

  // Remove state for document saving
  // const [isDocUpdating, setIsDocUpdating] = useState(false)
  // const [showDocConfirmation, setShowDocConfirmation] = useState(false)
  // const [docUpdateStatus, setDocUpdateStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  // const [docUpdateMessage, setDocUpdateMessage] = useState('')
  // const [docUpdateProgress, setDocUpdateProgress] = useState(0)



  // Memoize the handleSaveEdit function
  const handleSaveEdit = useCallback(() => {
    // Use requestAnimationFrame for smoother UI updates
    requestAnimationFrame(() => {
      if (!editingItem) {
        return;
      }

      // Batch state updates
      if (editingItem.id === -1) {
        setQuestion(editingItem.value);
      } else if (editingItem.id === -2) {
        setAnswer(editingItem.value);
        // If this is an answer edit and we're in audio mode, validate the new code
        if (isAudioAnswer && editingItem.value.trim()) {
          validateAudioCode(editingItem.value.trim());
        }
      } else {
        // Use functional update to avoid dependency on recentQA
        setRecentQA(prev =>
          prev.map(qa =>
            qa.id === editingItem.id
              ? { ...qa, [editingItem.field]: editingItem.value }
              : qa
          )
        );
      }

      // Close modal after state updates
      setEditingItem(null);
      setHasFocusedInput(false);
      
    });
  }, [editingItem, isAudioAnswer]);







  // Add effect to focus textarea and set cursor at the end when editing
  // Note: This is now handled by the EditModal component
  useEffect(() => {
    // This effect is kept for compatibility but the actual focus logic 
    // is now handled within the EditModal component
  }, [editingItem, hasFocusedInput])

  // Reset focus tracking when modal closes
  useEffect(() => {
    if (!editingItem) {
      setHasFocusedInput(false);
    }
  }, [editingItem]);



  // Function to focus input and set cursor at the end
  const focusInputAtEnd = (ref: React.RefObject<HTMLInputElement>) => {
    if (ref.current) {
      ref.current.focus()
      const length = ref.current.value.length
      ref.current.setSelectionRange(length, length)
    }
  }

  const handleStartEdit = (id: number, field: 'question' | 'answer', value: string) => {
    setEditingItem({ id, field, value })
    setHasFocusedInput(false)
    // Focus and set cursor at end will happen in useEffect
  }

  // handleSaveEdit is now defined above with useCallback

  const handleViewItem = (field: 'question' | 'answer', value: string) => {
    setViewingItem({ field, value })
  }

  const knowledgeItems = [
    {
      id: 1,
      title: 'Company FAQ',
      itemCount: 24,
      dateAdded: 'Apr 12, 2025',
      isActive: true
    },
    {
      id: 2,
      title: 'Product Information',
      itemCount: 36,
      dateAdded: 'Apr 10, 2025',
      isActive: true
    },
    {
      id: 3,
      title: 'Return Policy',
      itemCount: 8,
      dateAdded: 'Apr 5, 2025',
      isActive: false
    }
  ]

  // Function to validate audio code
  const validateAudioCode = async (audioCode: string) => {
    if (!audioCode.trim()) {
      setAudioValidation(null)
      return
    }

    setIsValidatingAudio(true)
    try {
      const response = await fetch('/api/audio/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ audioCode: audioCode.trim() })
      })

      if (response.ok && response.headers.get('X-Audio-Valid') === 'true') {
        // Response is processed audio blob
        const audioBlob = await response.blob()
        const previewUrl = URL.createObjectURL(audioBlob)
        
        const duration = parseInt(response.headers.get('X-Audio-Duration') || '0')
        const fileId = response.headers.get('X-Audio-FileId') || ''
        
        if (duration > 0) {
          setAudioValidation({
            valid: true,
            duration: duration,
            file_id: fileId,
            previewUrl: previewUrl
          })
        } else {
          // Invalid duration
          URL.revokeObjectURL(previewUrl)
          setAudioValidation(null)
          setAnswer('')
          setUpdateMessage(t('audio_code_incorrect'))
          setUpdateStatus('error')
          setTimeout(() => setUpdateStatus('idle'), 2000)
        }
      } else {
        // Handle JSON error response
        const errorData = await response.json()
        console.error('Audio validation failed:', errorData.error_msg)
        
        // Clear back to normal audio mode
        setAudioValidation(null)
        setAnswer('')
        setUpdateMessage(t('audio_code_incorrect'))
        setUpdateStatus('error')
        setTimeout(() => setUpdateStatus('idle'), 2000)
      }
    } catch (error) {
      console.error('Audio validation error:', error)
      // Clear back to normal audio mode on error
      setAudioValidation(null)
      setAnswer('')
      // Show error message for 2000ms
      setUpdateMessage(t('audio_code_incorrect'))
      setUpdateStatus('error')
      setTimeout(() => setUpdateStatus('idle'), 2000)
    } finally {
      setIsValidatingAudio(false)
    }
  }

  // Audio playback handlers for preview section
  const handlePreviewAudioClick = (previewUrl: string) => {
    const audioId = 'preview'
    // Reset Recently Added audio when playing preview
    setSelectedRecentAudioId(null)
    setIsRecentAudioPlaying(false)
    
    if (selectedPreviewAudioId === audioId) {
      // Same audio clicked - toggle play/pause
      setIsPreviewAudioPlaying(!isPreviewAudioPlaying)
    } else {
      // Different audio clicked - select new one and start playing
      setSelectedPreviewAudioId(audioId)
      setIsPreviewAudioPlaying(true)
    }
  }

  // Audio playback handlers for Recently Added section
  const handleRecentAudioClick = (id: number) => {
    // Reset preview audio when playing recent audio
    setSelectedPreviewAudioId(null)
    setIsPreviewAudioPlaying(false)
    
    if (selectedRecentAudioId === id) {
      // Same audio clicked - toggle play/pause
      setIsRecentAudioPlaying(!isRecentAudioPlaying)
    } else {
      // Different audio clicked - select new one and start playing
      setSelectedRecentAudioId(id)
      setIsRecentAudioPlaying(true)
    }
  }

  // Create preview URLs for Recently Added audio items
  useEffect(() => {
    const newUrls = new Map<number, string>()
    
    recentQA.forEach(qa => {
      if (qa.processedAudioBlob) {
        const previewUrl = URL.createObjectURL(qa.processedAudioBlob)
        newUrls.set(qa.id, previewUrl)
      }
    })
    
    // Clean up old URLs
    recentQAPreviewUrls.forEach((url, id) => {
      if (!newUrls.has(id)) {
        URL.revokeObjectURL(url)
      }
    })
    
    setRecentQAPreviewUrls(newUrls)
    
    // Cleanup on unmount
    return () => {
      newUrls.forEach(url => URL.revokeObjectURL(url))
    }
  }, [recentQA])

  // Handle audio delete confirmation
  const handleConfirmAudioDelete = () => {
    if (audioValidation?.previewUrl) {
      URL.revokeObjectURL(audioValidation.previewUrl)
    }
    setAudioValidation(null)
    setAnswer('')
    setShowAudioDeleteConfirm(false)
  }

  const handleAddQA = async () => {
    if (question.trim() && answer.trim()) {
      // For audio answers, check if validation passed
      if (isAudioAnswer && (!audioValidation?.valid)) {
        setUpdateMessage('Please provide a valid audio code')
        setUpdateStatus('error')
        setTimeout(() => setUpdateStatus('idle'), 3000)
        return
      }

      // Get processed audio blob for storage
      let processedAudioBlob: Blob | undefined
      if (isAudioAnswer && audioValidation?.valid && audioValidation.previewUrl) {
        try {
          // Convert preview URL back to blob for storage
          const response = await fetch(audioValidation.previewUrl)
          processedAudioBlob = await response.blob()
        } catch (error) {
          console.error('Error converting preview URL to blob:', error)
        }
      }

      setRecentQA(prev => [...prev, {
        id: Date.now(),
        question: question.trim(),
        answer: answer.trim(),
        isAudioAnswer: isAudioAnswer,
        audioDuration: isAudioAnswer && audioValidation?.valid ? audioValidation.duration : undefined,
        audioFileId: isAudioAnswer && audioValidation?.valid ? audioValidation.file_id : undefined,
        processedAudioBlob: processedAudioBlob,
        // Include photo info if a photo is selected
        photoInfo: selectedPhoto ? {
          id: selectedPhoto.id,
          photo_id: selectedPhoto.photo_id,
          photo_url: selectedPhoto.photo_url,
          full_photo_urls: selectedPhoto.full_photo_urls
        } : undefined
      }]);

      // Reset fields after adding
      setQuestion('');
      setAnswer('');
      // Reset the selected photo as well
      setSelectedPhoto(null);
      // Reset audio validation but keep audio mode
      if (audioValidation?.previewUrl) {
        URL.revokeObjectURL(audioValidation.previewUrl);
      }
      setAudioValidation(null);
      
    }
  }

  // Prompt for confirmation before update
  const handleUpdate = () => {
    if (recentQA.length === 0) {
      setUpdateMessage(t('no_questions_update'))
      setUpdateStatus('error')
      setTimeout(() => setUpdateStatus('idle'), 3000)
      return
    }
    if (recentQA.length > 20) {
      setUpdateMessage('Maximum 20 FAQs per update. Please remove some items.')
      setUpdateStatus('error')
      setTimeout(() => setUpdateStatus('idle'), 3000)
      return
    }
    setShowConfirmation(true)
  }

  // Note: blobToBase64 function is defined below in saveQAToSupabase

  // Helper function to convert blob to base64
  const blobToBase64 = (blob: Blob): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onerror = reject;
      reader.onload = () => {
        const base64String = reader.result as string;
        resolve(base64String.split(',')[1]); // Remove the data URL prefix
      };
      reader.readAsDataURL(blob);
    });
  }

  // Save Q&A items using API route (which uses PostgreSQL webhook)
  const saveQAToSupabase = async () => {
    setShowConfirmation(false);
    setIsUpdating(true);
    setUpdateStatus('loading');
    setUpdateProgress(0);
    setUpdateMessage(t('processing_additions'));

    const totalItems = recentQA.length;
    if (totalItems === 0) {
      setUpdateMessage(t('no_questions_update'));
      setUpdateStatus('error');
      setTimeout(() => setUpdateStatus('idle'), 3000);
      setIsUpdating(false);
      return;
    }

    // Start fake progress animation (clean increments: 10, 20, 30, etc.)
    const progressInterval = setInterval(() => {
      setUpdateProgress(prev => {
        if (prev >= 90) return prev; // Stop at 90% until real response
        return Math.min(90, prev + 10); // Clean 10% increments
      });
    }, 300);

    try {
      // Prepare knowledge items for API
      const knowledgeItems = []
      const audioCount = recentQA.filter(qa => qa.isAudioAnswer).length
      
      if (audioCount > 0) {
        setUpdateMessage(`Uploading ${audioCount} audio file${audioCount > 1 ? 's' : ''}...`);
      } else {
        setUpdateMessage('Preparing FAQs for upload...');
      }

      // Use FormData to send blobs
      const formData = new FormData()
      
      for (let i = 0; i < recentQA.length; i++) {
        const qa = recentQA[i]
        
        // Add FAQ data
        knowledgeItems.push({
          question: qa.question,
          answer: qa.answer,
          isAudioAnswer: qa.isAudioAnswer || false,
          audioFileId: qa.audioFileId,
          audioDuration: qa.audioDuration,
          photoInfo: qa.photoInfo,
          // Add index reference for blob mapping
          audioIndex: qa.isAudioAnswer && qa.processedAudioBlob ? i : null
        });
        
        // Add audio blob if it exists
        if (qa.isAudioAnswer && qa.processedAudioBlob) {
          formData.append(`audioBlob_${i}`, qa.processedAudioBlob, `audio_${i}.m4a`)
        }
      }

      // Add FAQ batch as JSON string
      formData.append('faqBatch', JSON.stringify(knowledgeItems))
      
      // Add sector and lang from knowledge cache to eliminate API cache lookup
      if (knowledgeData?.sector) {
        formData.append('sector', knowledgeData.sector)
      }
      if (knowledgeData?.clientLang) {
        formData.append('lang', knowledgeData.clientLang)
      }

      // Show processing message
      if (audioCount > 0) {
        setUpdateMessage('Uploading audio files and saving to database...');
      } else {
        setUpdateMessage('Saving FAQs to database...');
      }

      // Call new batch API route
      const response = await fetch('/api/knowledge/add-batch', {
        method: 'POST',
        body: formData,
      });

      let responseData = await response.json();
      
      // Handle array response format from N8N webhook
      if (Array.isArray(responseData) && responseData.length > 0) {
        responseData = responseData[0]
      }

      if (!response.ok || !responseData.success) {
        throw new Error(responseData.error_msg || 'Failed to save knowledge items');
      }

      // Clear progress interval and complete progress
      clearInterval(progressInterval);
      setUpdateProgress(100);
      
      // Show simple success message
      const count = recentQA.length;
      const plural = count !== 1 ? 's' : '';
      setUpdateMessage(t('update_success').replace('{count}', count.toString()).replace('{plural}', plural));
      setUpdateStatus('success');

      // Update local FAQ count for instant UI feedback
      const currentFaqCount = Number(knowledgeStats?.faqCount) || 0;
      const newFaqCount = currentFaqCount + count;
      updateFaqCountInState(newFaqCount);

      // Clear the recent QA list
      setRecentQA([]);

      // Show success message and reset
      setTimeout(() => {
        setUpdateStatus('idle');
        setIsUpdating(false);
      }, 1500);

    } catch (error: any) {
      console.error('Error updating knowledge base:', error);

      // Clear progress interval on error
      clearInterval(progressInterval);

      setUpdateStatus('error');
      setUpdateMessage(error.message || 'Failed to save knowledge items');

      setTimeout(() => {
        setUpdateStatus('idle');
        setIsUpdating(false);
      }, 3000);
    }
  };

  const handleDelete = (id: number) => {
    // Show confirmation modal instead of deleting immediately
    setItemToDelete(id);
    setShowDeleteConfirmation(true);
  }

  // Function to handle actual deletion after confirmation
  const confirmDelete = () => {
    if (itemToDelete === null) return;

    const qaToDelete = recentQA.find(qa => qa.id === itemToDelete);

    // Clean up audio blob URL if this is an audio item
    if (qaToDelete?.isAudioAnswer && recentQAPreviewUrls.has(itemToDelete)) {
      const audioUrl = recentQAPreviewUrls.get(itemToDelete);
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    }

    // Remove item after confirmation
    setRecentQA(prev => prev.filter(qa => qa.id !== itemToDelete));

    // Close the confirmation modal
    setShowDeleteConfirmation(false);
    setItemToDelete(null);
  }

  // Function to confirm switching to audio mode
  const confirmAudioMode = () => {
    setAnswer('');
    setIsAudioAnswer(true);
    setShowAudioModeWarning(false);
  }









  // Add useEffect to close product results when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchResultsRef.current && !searchResultsRef.current.contains(event.target as Node)) {
        setShowPhotoResults(false)
      }
    }

    if (showPhotoResults) {
      document.addEventListener('mousedown', handleClickOutside)
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showPhotoResults])

  // Extract photos from unified knowledge cache
  const photosData = knowledgeData?.photos || []

  // Function to search photos using API route (which uses PostgreSQL webhook)
  const searchPhotos = async (query: string) => {
    if (!query.trim()) {
      setPhotoSearchResults([])
      setShowPhotoResults(false)
      return
    }

    setIsSearching(true)
    try {
      // Check if we have photos data loaded from unified cache
      if (photosData.length > 0) {
        // Search locally in cached photos
        const filteredPhotos = photosData.filter(photo =>
          photo.photo_id.toLowerCase().includes(query.toLowerCase())
        ).slice(0, 5)

        if (filteredPhotos.length > 0) {
          setPhotoSearchResults(filteredPhotos)
          setShowPhotoResults(true)
          setIsSearching(false)
          return
        }
        // If local search returns no results but we have photos, try API search
      } else {
        // If we have no photos at all, immediately return empty results
        setPhotoSearchResults([])
        setShowPhotoResults(false)
        setIsSearching(false)
        return
      }

      // If local search had no results but we have photos, search via API route
      const response = await fetch('/api/knowledge/search-photos', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query, limit: 5 }),
      })

      const responseData = await response.json()

      if (!response.ok) {
        console.error('Error searching photos:', responseData.error_msg)
        return
      }

      // Use the raw SQL result directly (no transformation needed)
      const transformedPhotos = responseData.body

      setPhotoSearchResults(transformedPhotos)
      setShowPhotoResults(transformedPhotos.length > 0)
    } catch (error) {
      console.error('Error in searchPhotos:', error)
    } finally {
      setIsSearching(false)
    }
  }

  // Add function to clear photo search
  const clearPhotoSearch = () => {
    setPhotoSearchResults([]);
    setShowPhotoResults(false);
    setPhotoSearchQuery('');
  };

  // Add function to handle photo search input
  const handlePhotoSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value
    setPhotoSearchQuery(query)
    if (query.trim()) {
      searchPhotos(query)
    } else {
      clearPhotoSearch()
    }
  }

  // Add function to select a photo
  const handleSelectPhoto = (photo: {
    id: number,
    photo_id: string,
    photo_url: string[] | null,
    photo_file_path?: string[] | null
  }) => {
    // Get first photo URL as thumbnail for display, but store the full array
    const thumbnail = photo.photo_url && photo.photo_url.length > 0 ? photo.photo_url[0] : null

    // Show loading animation
    setIsPhotoLoading(true)

    // Clear search results and hide dropdown
    clearPhotoSearch()

    // Add a small delay to show the loading animation
    setTimeout(() => {
      // Set the selected photo
      setSelectedPhoto({
        id: photo.id,
        photo_id: photo.photo_id,
        photo_url: thumbnail,
        full_photo_urls: photo.photo_url // Store the complete array of photo URLs
      })

      // Hide loading animation after a short delay
      setTimeout(() => {
        setIsPhotoLoading(false)
      }, 50)
    }, 100)
  }

  // Add function to clear selected photo
  const handleClearSelectedPhoto = () => {
    setSelectedPhoto(null)
    clearPhotoSearch()
  }


  // Handle viewing an image gallery
  const handleViewImage = (imageUrls: string[] | null | undefined) => {
    if (!imageUrls || imageUrls.length === 0) {
      // Show a notification that there are no images to display
      setUpdateStatus('error');
      setUpdateMessage('No images available for this item.');
      setTimeout(() => setUpdateStatus('idle'), 3000);
      return;
    }

    setImageGallery({
      urls: imageUrls,
      currentIndex: 0
    });
  };

  // Navigate to previous image in gallery
  const showPreviousImage = () => {
    if (!imageGallery || imageGallery.urls.length <= 1) return;

    const newIndex = (imageGallery.currentIndex - 1 + imageGallery.urls.length) % imageGallery.urls.length;
    setImageGallery({ ...imageGallery, currentIndex: newIndex });
  };

  // Navigate to next image in gallery
  const showNextImage = () => {
    if (!imageGallery || imageGallery.urls.length <= 1) return;

    const newIndex = (imageGallery.currentIndex + 1) % imageGallery.urls.length;
    setImageGallery({ ...imageGallery, currentIndex: newIndex });
  };

  // Handle touch events for swipe in gallery
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd || !imageGallery) return;

    const minSwipeDistance = 50; // Minimum distance in pixels to be considered a swipe
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isLeftSwipe) {
      // Handle left swipe (next image)
      showNextImage();
    }

    if (isRightSwipe) {
      // Handle right swipe (previous image)
      showPreviousImage();
    }

    // Reset touch coordinates
    setTouchStart(null);
    setTouchEnd(null);
  };

  // Keyboard navigation for image gallery (arrow keys and escape)
  useEffect(() => {
    function handleKeyDown(event: KeyboardEvent) {
      if (!imageGallery) return;

      if (event.key === 'ArrowLeft') {
        showPreviousImage();
      } else if (event.key === 'ArrowRight') {
        showNextImage();
      } else if (event.key === 'Escape') {
        setImageGallery(null);
      }
    }

    if (imageGallery) {
      document.addEventListener('keydown', handleKeyDown);
    }
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    }
  }, [imageGallery]);

  // Simple scroll disable when any popup is open
  useEffect(() => {
    const hasOpenModal = showConfirmation || editingItem || viewingItem || imageGallery || showDeleteConfirmation || showAudioModeWarning || showAudioDeleteConfirm || (updateStatus !== 'idle')

    if (hasOpenModal) {
      document.body.style.overflow = 'hidden'
      // Pause any playing audio when modal opens
      setSelectedPreviewAudioId(null)
      setIsPreviewAudioPlaying(false)
      setSelectedRecentAudioId(null)
      setIsRecentAudioPlaying(false)
    } else {
      document.body.style.overflow = ''
    }

    return () => {
      document.body.style.overflow = ''
    }
  }, [showConfirmation, editingItem, viewingItem, imageGallery, showDeleteConfirmation, showAudioModeWarning, showAudioDeleteConfirm, updateStatus])

  return (
    <div className={themeConfig.pageBackground}>
      {/* Background effects */}
      {themeConfig.backgroundEffects}

      {/* Theme-aware Header */}
      <header className="relative">
        <div className="container mx-auto px-3 py-3">
          <div
            className={`relative ${themeConfig.card} rounded-2xl px-4 py-3 border ${themeConfig.border} transition-all duration-300 overflow-hidden`}
            style={theme === 'dark' ? {
              boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
            } : {
              boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
            }}
          >
            {/* Content */}
            <div className="relative z-10 flex justify-center items-center">
              <LinkButton 
                href="/dashboard" 
                variant="ghost" 
                className="p-0 hover:bg-transparent active:scale-95"
              >
                <img
                  src={themeConfig.logo}
                  alt="Chhlat Bot"
                  className="h-8 w-auto transition-transform duration-300 hover:scale-105"
                />
              </LinkButton>
            </div>
          </div>
        </div>
      </header>

      <div className="flex-grow container mx-auto px-4 py-2">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Content header with title and back button */}
          <DashboardHeader 
            backHref="/dashboard"
            titleKey="ai_brain"
          />

          {/* Top Section */}
          <KnowledgeTopSection
            currentPath={pathname}
            totalFaqs={totalFaqs}
            totalFaqsLimit={totalFaqsLimit}
            photoCount={photoCount}
            photoLimit={photoLimit}
            faqUsagePercentage={faqUsagePercentage}
            photoUsagePercentage={photoUsagePercentage}
            isLoadingCount={isLoadingCount}
          />

          {/* Business Insights Section */}
          <div
            className={`relative ${themeConfig.card} rounded-2xl p-6 mb-6 border ${themeConfig.border} transition-all duration-300 group overflow-hidden`}
            style={theme === 'dark' ? {
              boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
            } : {
              boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
            }}
          >

            <div className="relative z-10">
            <div className="flex justify-between items-center mb-6">
              <h2 className={`text-xl font-bold font-title ${themeConfig.text}`}>{t('business_insights')}</h2>
              <LinkButton
                href="/dashboard/knowledge/knowledgeBase"
                variant="secondary"
                size="sm"
                className="text-xs sm:text-base p-2 px-3 border-2"
              >
                {t('manage')}
              </LinkButton>
            </div>
            {/* <p className={`${themeConfig.textSecondary} text-sm mb-4 md:mb-6 font-body`}>
              {t('add_business_info')}
            </p> */}

            {/* Help Section */}
            <HelpSection 
              youtubeUrl="#" // TODO: Replace with actual YouTube URL
              message={t('add_business_info')}
              clientLang={clientLang || 'English'}
            />

            {/* Photo Search Bar */}
            <PhotoSearchBar
              searchQuery={photoSearchQuery}
              onSearchChange={(query) => {
                setPhotoSearchQuery(query);
                if (query.trim()) {
                  searchPhotos(query);
                } else {
                  setPhotoSearchResults([]);
                  setShowPhotoResults(false);
                }
              }}
              searchResults={photoSearchResults}
              isSearching={isSearching}
              showResults={showPhotoResults}
              onSelectPhoto={handleSelectPhoto}
              onFocus={() => {
                if (photoSearchResults.length > 0) {
                  setShowPhotoResults(true);
                } else if (photoSearchQuery) {
                  searchPhotos(photoSearchQuery);
                }
              }}
              placeholder={t('search_photo_placeholder')}
            />

            {/* Selected Photo Display */}
            <SelectedPhotoDisplay
              selectedPhoto={selectedPhoto}
              isLoading={isPhotoLoading}
              onRemovePhoto={handleClearSelectedPhoto}
              onViewImage={handleViewImage}
            />

            <div className="grid grid-cols-1 sm:grid-cols-12 gap-3 mb-6">
              {/* Question Trigger */}
              <div className="sm:col-span-5">
                <div
                  className={`px-2 md:px-4 py-2 ${themeConfig.secondCard} border ${themeConfig.border} rounded-lg ${themeConfig.text} font-body focus:outline-none ${themeConfig.borderActive} cursor-pointer ${themeConfig.borderHover} flex items-center min-h-[42px]`}
                  onClick={() => {
                    setEditingItem({
                      id: -1,
                      field: 'question',
                      value: question
                    });
                  }}
                >
                  {question ?
                    <span className="truncate font-body">{question}</span> :
                    <span className={`${themeConfig.textMuted} truncate font-body`}>{t('enter_question')}</span>
                  }
                </div>
              </div>
              {/* Answer Trigger */}
              <div className="sm:col-span-5 relative">
                <div
                  className={`px-2 pr-10 md:px-4 md:pr-10 py-2 ${themeConfig.secondCard} border ${themeConfig.border} rounded-lg ${themeConfig.text} focus:outline-none ${themeConfig.borderActive} ${audioValidation?.valid ? 'cursor-default' : `cursor-pointer ${themeConfig.borderHover}`} flex items-center min-h-[42px] overflow-hidden min-w-0`}
                  onClick={audioValidation?.valid ? undefined : () => {
                    setEditingItem({
                      id: -2,
                      field: 'answer',
                      value: answer
                    });
                  }}
                >
                  {/* Regular input display */}
                  {answer ? (
                    <div className="flex items-center w-full">
                      {isAudioAnswer && audioValidation?.valid && audioValidation.previewUrl ? (
                        <AudioPlayer
                          audioUrl={audioValidation.previewUrl}
                          compact={true}
                          className="mr-2"
                          isSelected={selectedPreviewAudioId === 'preview'}
                          isPlaying={selectedPreviewAudioId === 'preview' && isPreviewAudioPlaying}
                          onClick={() => handlePreviewAudioClick(audioValidation.previewUrl!)}
                        />
                      ) : isAudioAnswer && audioValidation?.valid ? (
                        <div className="flex items-center mr-2 flex-shrink-0">
                          <svg 
                            className={`w-4 h-4 mr-1 ${themeConfig.text}`}
                            fill="currentColor" 
                            viewBox="0 0 24 24"
                          >
                            <rect x="2" y="8" width="2" height="8" rx="1"/>
                            <rect x="6" y="5" width="2" height="14" rx="1"/>
                            <rect x="10" y="10" width="2" height="4" rx="1"/>
                            <rect x="14" y="3" width="2" height="18" rx="1"/>
                            <rect x="18" y="7" width="2" height="10" rx="1"/>
                            <rect x="22" y="12" width="2" height="2" rx="1"/>
                          </svg>
                          <span className={`text-sm ${themeConfig.textMuted}`}>
                            {audioValidation.duration}s
                          </span>
                        </div>
                      ) : isValidatingAudio ? (
                        <div className="mr-2 w-4 h-4 border-2 border-jade-purple border-t-transparent rounded-full animate-spin flex-shrink-0"></div>
                      ) : null}
                      <span className="truncate flex-1 min-w-0 font-body">{isAudioAnswer && (audioValidation?.valid || isValidatingAudio) ? '' : answer}</span>
                    </div>
                  ) : (
                    <span className={`${themeConfig.textMuted} truncate pr-12 min-w-0 font-body`}>
                      {isAudioAnswer ? t('paste_audio_code') : t('enter_reply')}
                    </span>
                  )}
                  
                  {/* Microphone/Trash Icon */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent triggering the edit modal
                      if (isAudioAnswer && audioValidation?.valid) {
                        // Show confirmation before clearing audio
                        setShowAudioDeleteConfirm(true);
                      } else if (!isAudioAnswer) {
                        // Check if there's existing text
                        if (answer.trim()) {
                          setShowAudioModeWarning(true);
                        } else {
                          setIsAudioAnswer(true);
                        }
                      } else {
                        setIsAudioAnswer(false);
                      }
                    }}
                    className={`absolute right-2 top-1/2 transform -translate-y-1/2 p-1.5 rounded-full transition-all duration-200 ${
                      isAudioAnswer && audioValidation?.valid
                        ? 'bg-red-500/20 text-red-500 hover:bg-red-500/30'
                        : isAudioAnswer 
                          ? 'bg-jade-purple-dark text-white hover:bg-jade-purple-dark/80' 
                          : `${themeConfig.textMuted} hover:text-jade-purple hover:bg-jade-purple/10`
                    }`}
                    title={
                      isAudioAnswer && audioValidation?.valid 
                        ? 'Clear audio code'
                        : isAudioAnswer 
                          ? 'Audio mode active - click to switch to text' 
                          : 'Switch to audio answer'
                    }
                  >
                    {isAudioAnswer && audioValidation?.valid ? (
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    ) : (
                      <FaMicrophone className="w-4 h-4" />
                    )}
                  </button>
                </div>
              </div>
              {/* Add Button */}
              <div className="sm:col-span-2 flex items-center justify-center">
                <Button
                  onClick={handleAddQA}
                  variant="primary"
                  size="md"
                  className="text-xs sm:text-base w-full"
                  disabled={!question.trim() || !answer.trim() || (isAudioAnswer && (!audioValidation?.valid || isValidatingAudio))}
                >
                  {t('add')}
                </Button>
              </div>
            </div>

            {/* Recently Added Section - now part of the same container */}
            <div className={`flex justify-between items-center mb-3 border-t ${themeConfig.divider} pt-6`}>
              <h3 className={`text-lg font-semibold font-title ${themeConfig.text}`}>{t('recently_added')}</h3>
              <Button
                onClick={handleUpdate}
                variant="primary"
                size="md"
                className="text-xs sm:text-base"
                disabled={isUpdating || recentQA.length === 0}
              >
                {isUpdating ? t('updating') : t('update')}
              </Button>
            </div>

            {/* Column Headers */}
            <div className={`flex border-b ${themeConfig.divider} py-3 font-semibold ${themeConfig.textSecondary} font-body`}>
              <div className="w-[5%] px-2 text-left"></div>
              <div className="w-[35%] px-2 text-left">{t('question')}</div>
              <div className="w-[35%] px-2 text-left">{t('reply')}</div>
              <div className="w-[15%] px-2 text-left"></div>
              <div className="w-[10%] px-2 text-center"></div>
            </div>

            {/* List Items */}
            <div className="w-full">
              {recentQA.length > 0 ? (
                recentQA.map((qa, index) => (
                  <div key={qa.id} className={`flex border-b ${themeConfig.divider} py-3 items-center`}>
                    <div className={`w-[5%] px-2 text-left ${themeConfig.textMuted} font-body`}>
                      {index + 1}
                    </div>
                    <div
                      className="w-[35%] px-2 py-1"
                    >
                      <div
                        className={`h-full w-full truncate overflow-hidden min-w-0 px-2 py-1.5 ${themeConfig.secondCard} border-2 ${themeConfig.border} rounded-lg ${themeConfig.secondCardHover} flex items-center group cursor-pointer transition-all`}
                        title={qa.question}
                        onClick={() => handleStartEdit(qa.id, 'question', qa.question)}
                      >
                        <span className={`flex-1 truncate min-w-0 ${themeConfig.textSecondary} font-body`}>{qa.question}</span>
                        <svg className={`w-4 h-4 ml-2 ${themeConfig.textMuted} group-hover:text-jade-purple invisible group-hover:visible`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </div>
                    </div>
                    <div
                      className="w-[35%] px-2 py-1"
                    >
                      <div
                        className={`h-full w-full truncate overflow-hidden min-w-0 px-2 py-1.5 ${themeConfig.secondCard} border-2 ${themeConfig.border} rounded-lg ${themeConfig.secondCardHover} flex items-center group transition-all`}
                        title={qa.answer}
                        onClick={qa.isAudioAnswer ? undefined : () => handleStartEdit(qa.id, 'answer', qa.answer)}
                      >
                        {qa.isAudioAnswer ? (
                          recentQAPreviewUrls.has(qa.id) ? (
                            <AudioPlayer
                              audioUrl={recentQAPreviewUrls.get(qa.id)!}
                              compact={true}
                              isSelected={selectedRecentAudioId === qa.id}
                              isPlaying={selectedRecentAudioId === qa.id && isRecentAudioPlaying}
                              onClick={() => handleRecentAudioClick(qa.id)}
                            />
                          ) : (
                            <div className="flex items-center">
                              <svg 
                                className={`w-4 h-4 mr-1 ${themeConfig.text}`}
                                fill="currentColor" 
                                viewBox="0 0 24 24"
                              >
                                <rect x="2" y="8" width="2" height="8" rx="1"/>
                                <rect x="6" y="5" width="2" height="14" rx="1"/>
                                <rect x="10" y="10" width="2" height="4" rx="1"/>
                                <rect x="14" y="3" width="2" height="18" rx="1"/>
                                <rect x="18" y="7" width="2" height="10" rx="1"/>
                                <rect x="22" y="12" width="2" height="2" rx="1"/>
                              </svg>
                              <span className={`text-sm ${themeConfig.textMuted} flex-shrink-0`}>
                                {qa.audioDuration ? `${t('voice')} ${qa.audioDuration}s` : t('voice')}
                              </span>
                            </div>
                          )
                        ) : (
                          <>
                            <span className={`flex-1 truncate min-w-0 ${themeConfig.textSecondary} font-body`}>{qa.answer}</span>
                            <svg className={`w-4 h-4 ml-2 ${themeConfig.textMuted} group-hover:text-jade-purple invisible group-hover:visible`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                          </>
                        )}
                      </div>
                    </div>
                    {/* Photo Column */}
                    <div className="w-[15%] px-2 py-1">
                      {qa.photoInfo ? (
                        <div className="h-full w-full px-3 py-1.5 rounded-lg flex items-center justify-center">
                          {/* Photo Thumbnail - Optimized and clickable to view gallery */}
                          <PhotoThumbnail
                            photo={{
                              photo_url: qa.photoInfo.photo_url ? [qa.photoInfo.photo_url] : null,
                              photo_id: qa.photoInfo.photo_id
                            }}
                            className={`w-10 h-10 border ${themeConfig.border}`}
                            onClick={() => handleViewImage(qa.photoInfo?.full_photo_urls || (qa.photoInfo?.photo_url ? [qa.photoInfo.photo_url] : null))}
                          />
                        </div>
                      ) : null}
                    </div>
                    <div className="w-[10%] px-2 flex justify-center">
                      <button
                        className="p-2 rounded-full bg-red-500/20 hover:bg-red-500/30 text-red-500 transition-colors"
                        onClick={() => handleDelete(qa.id)}
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </div>
                ))
              ) : (
                <div className={`py-8 text-center ${themeConfig.textMuted}`}>
                  {t('no_questions_update')}
                </div>
              )}
            </div>
            </div>
          </div>

        </motion.div>
      </div>

      {/* FAQ Update Status Overlay */}
      <UpdateStatusOverlay
        updateStatus={updateStatus}
        updateProgress={updateProgress}
        updateMessage={updateMessage}
        onClose={() => setUpdateStatus('idle')}
        loadingText={`${t('updating')}...`}
        completeText={t('complete')}
        successText={t('success')}
        errorText={t('error')}
      />

      {/* Confirmation Modal */}
      <UpdateConfirmationModal
        showConfirmation={showConfirmation}
        onCancel={() => setShowConfirmation(false)}
        onConfirm={saveQAToSupabase}
        count={recentQA.length}
      />

      {/* Edit Modal */}
      <EditModal
        editingItem={editingItem}
        hasFocusedInput={hasFocusedInput}
        onValueChange={(value) => {
          setEditingItem(prev => prev ? {...prev, value} : null);
        }}
        onInputFocus={() => setHasFocusedInput(true)}
        onSave={handleSaveEdit}
        onClose={() => setEditingItem(null)}
      />
      {/* View Modal */}
      <ViewModal
        viewingItem={viewingItem}
        questions={recentQA.map(qa => ({ question: qa.question, answer: qa.answer }))}
        onClose={() => setViewingItem(null)}
      />


      <DashboardFooter />


      {/* Image Gallery Modal */}
      <ImageGalleryModal
        imageGallery={imageGallery}
        onClose={() => setImageGallery(null)}
        onImageChange={(index) => {
          setImageGallery(prev => prev ? { ...prev, currentIndex: index } : null);
        }}
        onPrevious={showPreviousImage}
        onNext={showNextImage}
      />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        deleteConfirm={itemToDelete ? { id: itemToDelete } : null}
        isDeleting={false}
        onCancel={() => {
          setShowDeleteConfirmation(false);
          setItemToDelete(null);
        }}
        onConfirmDelete={confirmDelete}
      />

      {/* Audio Mode Warning Modal */}
      <AudioModeWarningModal
        showAudioModeWarning={showAudioModeWarning}
        onCancel={() => setShowAudioModeWarning(false)}
        onConfirm={confirmAudioMode}
      />

      {/* Audio Delete Confirmation Modal */}
      <DeleteAudioConfirmationModal
        showDeleteAudioConfirm={showAudioDeleteConfirm}
        onCancel={() => setShowAudioDeleteConfirm(false)}
        onConfirmDelete={handleConfirmAudioDelete}
      />

    </div>
  )
}