# Knowledge Management System

Handles FAQ creation, audio processing, and photo attachment workflows with batch processing capabilities.

## System Overview

### Core Features
- **FAQ Creation**: Text and audio answers with photo attachments
- **Batch Processing**: Up to 10 FAQs per batch with audio validation
- **Audio Pipeline**: Audio validation → Direct R2 upload (parallel processing)
- **Photo Integration**: Search and attach photos from knowledge cache
- **Real-time Updates**: Optimistic UI with local state mutations

### Architecture
- **Primary Database**: Direct PostgreSQL with connection pooling
- **Data Source**: Unified knowledge cache via `useKnowledgeData()` hook
- **Cache Strategy**: 30-minute server cache with `knowledge_${authId}` key
- **API Endpoint**: `/api/user/data?cache=knowledge`
- **State Management**: Local batch state with optimistic updates
- **Webhooks**: Fire-and-forget for analytics/external systems only

## FAQ Management

### Batch Processing Flow
1. **Local Creation**: Add FAQs to local state (max 10 per batch)
2. **Validation**: Validate questions, answers, and audio codes
3. **API Processing**: Send batch to `/api/knowledge/add-batch`
4. **Audio Processing**: Process all audio FAQs in parallel (no chunking)
5. **PostgreSQL Transaction**: Atomic insertion of all successful FAQs
6. **Cache Updates**: Update server cache with new FAQ counts
7. **Background Webhook**: Fire-and-forget analytics call
8. **State Updates**: Update local FAQ count optimistically

### Validation Rules
- **Questions**: Required, non-empty strings
- **Text Answers**: Required for non-audio FAQs
- **Audio Codes**: Required for audio FAQs, validated via audio API
- **Batch Size**: Maximum 10 FAQs per batch
- **Audio Blobs**: Required for audio FAQs (pre-processed)

### Error Handling
- Individual FAQ validation with specific error messages
- Batch processing with partial failure support
- Audio processing errors handled gracefully

## Audio Processing

### Audio Pipeline
1. **Validation**: Audio code validation via `/api/audio/validate`
2. **Blob Processing**: Pre-processed audio blobs from client
3. **Parallel Upload**: Direct R2 upload using AWS S3 SDK
4. **Path Generation**: Unique file paths with `audios/${authId}/${audioCode}-${uuid}.m4a`
5. **Public URL**: Generate Cloudflare R2 public URLs for playback

### Audio Code Validation
- **Source**: Audio validation API endpoint
- **Returns**: Duration, file_id, and processed audio blob
- **Reusability**: Codes can be reused (not marked as consumed)
- **Processing**: Returns ready-to-upload audio blob
- **Debouncing**: 500ms delay for real-time validation

### Performance Optimizations
- **Parallel Processing**: All audio FAQs processed simultaneously via Promise.allSettled
- **Direct R2 Upload**: Bypass HTTP overhead with AWS S3 SDK
- **Error Isolation**: Individual FAQ failures don't affect batch
- **Connection Pooling**: PostgreSQL pool with 20 max connections
- **Atomic Transactions**: Ensure all FAQs succeed or fail together

## Photo Management

### Search Strategy
- **Primary**: Local search in cached photos from knowledge cache
- **Fallback**: Direct PostgreSQL search via `/api/knowledge/search-photos` if cache miss
- **Performance**: PostgreSQL queries with proper indexing
- **Scope**: User-scoped search using client_id (only own photos)
- **Limits**: 5 results for search performance

### Photo Attachment
- **Selection**: Store photo_id, thumbnail URL, and full URL array
- **Display**: Thumbnail for UI, full array for gallery modal
- **Integration**: Attach to FAQs during batch processing
- **Clearing**: Reset selection state and search results

## Real-time Updates

### Optimistic UI Pattern
- **Photo Operations**: `addPhotoToState()`, `updatePhotoInState()`, `removePhotoFromState()`
- **FAQ Count**: Update count and usage percentage immediately
- **Background Sync**: Server synchronization happens asynchronously
- **Rollback**: Error handling can revert optimistic changes

### State Management
- **Local State**: Immediate UI updates for better UX
- **Cache Updates**: Update knowledge cache after successful operations
- **Consistency**: Ensure UI state matches server state eventually

## API Endpoints

### POST `/api/knowledge/add-batch` - FAQ Batch Processing
**Purpose**: Process up to 10 FAQs per batch with audio and photo support
**Architecture**: PostgreSQL-first with background webhooks
**Features**:
- Mixed text and audio FAQ processing with parallel audio upload
- Photo attachment support with full URL arrays
- PostgreSQL transactions for atomicity
- Partial failure handling with detailed error reporting
- Server cache updates for instant UI feedback
- Fire-and-forget webhooks for analytics

### POST `/api/audio/validate` - Audio Code Validation
**Purpose**: Validate audio codes and return processed blobs
**Returns**: Duration, file_id, and ready-to-upload audio blob
**Features**:
- Real-time validation with debouncing
- Audio code reusability
- Pre-processed audio blob generation for R2 upload

### POST `/api/knowledge/search-photos` - Photo Search
**Purpose**: Search photos by photo_id with direct PostgreSQL queries
**Features**:
- User-scoped search using client_id (own photos only)
- 5 result limit for performance
- Direct PostgreSQL JSON aggregation
- Fallback for cache misses

### Common Patterns
- **PostgreSQL First**: Direct database operations with connection pooling
- **Authentication**: All endpoints require user authentication with client_id
- **Error Handling**: Comprehensive error messages and status codes
- **Background Webhooks**: Fire-and-forget for non-critical operations

## Performance Strategy

### Caching Layers
1. **Server Cache**: 30-minute TTL for database queries
2. **Hook Cache**: Request deduplication and local state
3. **Component State**: Instant UI updates with optimistic rendering

### Processing Optimizations
- **Parallel Audio Upload**: Process all audio FAQs simultaneously via Promise.allSettled
- **Direct R2 Access**: Bypass HTTP layer for file uploads
- **Local Search**: Search cached photos before API calls
- **Request Deduplication**: Prevent duplicate concurrent requests
- **Batch Processing**: Handle up to 10 FAQs per batch with PostgreSQL transactions

### UI Performance
- **Optimistic Updates**: Immediate UI feedback without server round-trips
- **Debounced Validation**: 500ms delay for audio code validation
- **Lazy Loading**: Load photos only when needed

## Implementation Status

### Core Features ✅
- FAQ batch processing (up to 10 per batch) with PostgreSQL transactions
- Audio validation and direct R2 upload pipeline
- Photo search and attachment system with PostgreSQL backend
- Real-time UI updates with optimistic rendering
- 3-tier caching strategy with server-side cache updates

### Integration Points
- **Authentication**: User-scoped operations with `authId` and `clientId`
- **Database**: Direct PostgreSQL operations with connection pooling
- **Caching**: Server cache updates after successful database operations
- **Storage**: R2 for audio files with direct AWS S3 SDK upload
- **Analytics**: Fire-and-forget webhooks for external integrations

### Key Patterns
1. Validate all inputs before processing
2. Use PostgreSQL transactions for batch FAQ operations
3. Process audio files in parallel for performance
4. Handle individual failures without breaking the batch
5. Cache photo search results locally
6. Use optimistic UI updates for better UX
7. Update server cache after successful database operations
8. Use fire-and-forget webhooks for non-critical operations