CREATE TABLE public.error_logs (
  id SERIAL NOT NULL,
  client_id text NOT NULL,
  platform TEXT NULL,
  customer_id TEXT NULL,
  tg_name TEXT NULL,
  tg_id TEXT NULL,
  flag TEXT NULL,
  sector TEXT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  plan TEXT NULL,
  
  CONSTRAINT error_logs_pkey PRIMARY KEY (id),
  CONSTRAINT error_logs_client_id_fkey FOREIGN KEY (client_id) REFERENCES clients (client_id)
);

-- Useful indexes for error log queries
CREATE INDEX IF NOT EXISTS idx_error_logs_client_id ON public.error_logs (client_id);
CREATE INDEX IF NOT EXISTS idx_error_logs_created_at ON public.error_logs (created_at DESC);
CREATE INDEX IF NOT EXISTS idx_error_logs_platform ON public.error_logs (platform) WHERE platform IS NOT NULL;
-- No updated_at trigger needed since error logs are append-only