import './globals.css'
import type { Metadata } from 'next'
import { headers, cookies } from 'next/headers'
import { fontVariables } from '@/styles/fonts'
import { AuthProvider } from '@/context/AuthContext'
import { LanguageProvider } from '@/context/LanguageContext'
import { LoadingProvider } from '@/context/LoadingContext'
import { ThemeProvider } from '@/context/ThemeContext'
import { generateMetadata as generateStaticMetadata } from '@/lib/metadata'
import MetaUpdater from '@/components/MetaUpdater'
import StructuredData from '@/components/StructuredData'

// Dynamic metadata generation based on user language preference
export async function generateMetadata(): Promise<Metadata> {
  // Detect language server-side for proper SEO and browser tab display
  const cookieStore = cookies()
  const headersList = headers()
  
  // Priority: 1. <PERSON>ie preference, 2. Accept-Language header, 3. Default to English
  let language: 'km' | 'en' = 'en'
  
  // Check saved language preference
  const savedLanguage = cookieStore.get('uiLanguage')?.value
  if (savedLanguage === 'km' || savedLanguage === 'en') {
    language = savedLanguage
  } else {
    // Fallback to Accept-Language header for new users
    const acceptLanguage = headersList.get('accept-language') || ''
    if (acceptLanguage.toLowerCase().includes('km')) {
      language = 'km'
    }
    // Default to 'en' for international audience
  }
  
  return generateStaticMetadata(language)
}

// Viewport configuration
export const viewport = {
  width: 'device-width',
  initialScale: 1,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="km" className={`scroll-smooth ${fontVariables}`} suppressHydrationWarning>
      <head>
        {/* Cloudflare Web Analytics will be automatically injected via dashboard */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                try {
                  // Set theme
                  const theme = localStorage.getItem('uiTheme') || 'dark';
                  document.documentElement.classList.add(theme + '-theme');
                  
                  // Set language
                  let lang = localStorage.getItem('uiLanguage');
                  if (!lang || (lang !== 'km' && lang !== 'en')) {
                    // Detect browser language for new users
                    const browserLang = navigator.language.toLowerCase();
                    if (browserLang.startsWith('km')) {
                      lang = 'km';
                    } else {
                      lang = 'en'; // Default for international audience
                    }
                  }
                  document.documentElement.lang = lang;
                } catch (e) {
                  document.documentElement.lang = 'en'; // Fallback
                }
              })();
            `,
          }}
        />
      </head>
      <body className="antialiased" suppressHydrationWarning>
        <AuthProvider>
          <LanguageProvider>
            <ThemeProvider>
              <LoadingProvider>
                <MetaUpdater />
                <StructuredData />
                {children}
              </LoadingProvider>
            </ThemeProvider>
          </LanguageProvider>
        </AuthProvider>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Initialize theme class on body to prevent flash of unstyled content
              (function() {
                try {
                  const savedTheme = localStorage.getItem('uiTheme') || 'dark';
                  document.body.className = document.body.className.replace(/\\b(light-theme|dark-theme)\\b/g, '');
                  document.body.classList.add(savedTheme + '-theme');
                } catch (e) {
                  console.error('Error initializing theme:', e);
                }
              })();
            `,
          }}
        />
      </body>
    </html>
  )
}