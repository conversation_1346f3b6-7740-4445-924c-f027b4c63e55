'use client'

import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'

interface UpdateStatusOverlayProps {
  updateStatus: 'idle' | 'loading' | 'success' | 'error'
  updateProgress: number
  updateMessage: string
  onClose: () => void
  // Optional customizable text labels
  loadingText?: string
  completeText?: string
  successText?: string
  errorText?: string
  // Optional customization
  spinnerSize?: 'sm' | 'md' | 'lg'
  hideProgress?: boolean
}

export default function UpdateStatusOverlay({
  updateStatus,
  updateProgress,
  updateMessage,
  onClose,
  loadingText,
  completeText,
  successText,
  errorText,
  spinnerSize = 'md',
  hideProgress = false
}: UpdateStatusOverlayProps) {
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()

  // Get spinner size classes
  const getSpinnerClasses = () => {
    switch (spinnerSize) {
      case 'sm': return 'w-8 h-8'
      case 'lg': return 'w-16 h-16'
      default: return 'w-10 h-10'
    }
  }

  if (updateStatus === 'idle') return null

  return (
    <div
      className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50"
      onClick={() => updateStatus !== 'loading' && onClose()}
    >
      <div
        className={`relative ${
          updateStatus === 'loading' 
            ? `${theme === 'light' ? 'bg-white' : 'bg-jade-purple/[0.8]'} border-jade-purple/50` 
            : updateStatus === 'success'
              ? `${theme === 'light' ? 'bg-white' : 'bg-green-700/[0.8]'} border-green-700/50`
              : `${theme === 'light' ? 'bg-white' : 'bg-red-900/[0.8]'} border-red-900/50`
        } backdrop-blur-xl rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden`}
        onClick={(e) => e.stopPropagation()}
      >
        {theme === 'dark' && (
          <div className={`absolute inset-0 bg-gradient-to-br ${
            updateStatus === 'loading'
              ? 'from-jade-purple/30'
              : updateStatus === 'success'
                ? 'from-green-700/30'
                : 'from-red-900/30'
          } to-transparent opacity-50 rounded-2xl`}></div>
        )}
        
        <div className="relative z-10">
          {updateStatus === 'loading' ? (
            <div className="flex flex-col items-center text-center">
              <p className={`text-lg font-semibold mb-3 ${themeConfig.text}`}>
                {loadingText || t('changing_knowledge_base')}
              </p>
              {!hideProgress && (
                <>
                  <div className={`w-full ${theme === 'light' ? themeConfig.bg : 'bg-white/30'} rounded-full h-3 mb-1`}>
                    <div
                      className="bg-jade-purple h-3 rounded-full transition-all duration-300"
                      style={{ width: `${updateProgress}%` }}
                    ></div>
                  </div>
                  <p className={`text-sm ${themeConfig.textSecondary}`}>
                    {updateProgress}% {completeText || t('complete')}
                  </p>
                </>
              )}
              <p className={`text-sm mt-2 ${themeConfig.textSecondary}`}>{updateMessage}</p>
            </div>
          ) : (
            <div className="flex flex-col items-center">
              <div className={`w-12 h-12 rounded-full flex items-center justify-center mb-4 ${
                updateStatus === 'success' 
                  ? 'bg-green-500/40' 
                  : 'bg-red-400/40'
              }`}>
                {updateStatus === 'success' ? (
                  <svg className={`w-6 h-6 ${theme === 'light' ? 'text-green-600' : 'text-green-300'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                ) : (
                  <svg className={`w-6 h-6 ${theme === 'light' ? 'text-red-600' : 'text-red-300'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                )}
              </div>
              <p className={`text-lg font-semibold mb-1 ${themeConfig.text}`}>
                {updateStatus === 'success' ? (successText || t('success')) : (errorText || t('error'))}
              </p>
              <p className={`${themeConfig.textSecondary} text-center text-sm`}>{updateMessage}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}