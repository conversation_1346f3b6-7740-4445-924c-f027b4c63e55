'use client'

import { useRef, useEffect } from 'react'
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'

// Photo Thumbnail component - reuse from knowledge page
const PhotoThumbnail = ({
  photo,
  className = "w-10 h-10",
  onClick
}: {
  photo: { photo_url: string[] | null, photo_id: string },
  className?: string,
  onClick?: () => void
}) => {
  const themeConfig = useThemeConfig()

  return (
    <div
      className={`${className} ${themeConfig.skeletonElement} rounded overflow-hidden flex-shrink-0 relative ${onClick ? 'cursor-pointer' : ''}`}
      onClick={onClick}
    >
      {photo.photo_url && photo.photo_url.length > 0 ? (
        <img
          src={photo.photo_url[0]}
          alt={photo.photo_id}
          className="w-full h-full object-cover"
          loading="lazy"
          decoding="async"
        />
      ) : (
        <div className={`w-full h-full flex items-center justify-center ${themeConfig.skeletonElement} ${themeConfig.textMuted}`}>
          <span className="text-xs">No Image</span>
        </div>
      )}
    </div>
  )
}

export interface PhotoData {
  id: number
  photo_id: string
  photo_url: string[] | null
  photo_file_path?: string[] | null
}

interface PhotoSearchBarProps {
  searchQuery: string
  onSearchChange: (query: string) => void
  searchResults: PhotoData[]
  isSearching: boolean
  showResults: boolean
  onSelectPhoto: (photo: PhotoData) => void
  onFocus?: () => void
  placeholder?: string
  disabled?: boolean
  className?: string
}

export default function PhotoSearchBar({
  searchQuery,
  onSearchChange,
  searchResults,
  isSearching,
  showResults,
  onSelectPhoto,
  onFocus,
  placeholder,
  disabled = false,
  className = "mb-4"
}: PhotoSearchBarProps) {
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()
  const searchResultsRef = useRef<HTMLDivElement>(null)

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onSearchChange(e.target.value)
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchResultsRef.current && !searchResultsRef.current.contains(event.target as Node)) {
        // Note: This component doesn't manage its own state, parent should handle closing
      }
    }

    if (showResults) {
      document.addEventListener('mousedown', handleClickOutside)
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showResults])

  return (
    <div className={`relative ${className}`}>
      <div className="relative">
        <input
          type="text"
          value={searchQuery}
          onChange={handleSearchChange}
          onFocus={onFocus}
          placeholder={placeholder || t('search_photo_placeholder')}
          disabled={disabled}
          className={`w-full px-3 py-2 ${themeConfig.secondCard} border ${themeConfig.border} rounded-lg ${themeConfig.text} font-body focus:outline-none ${disabled ? 'cursor-not-allowed opacity-70' : `${themeConfig.borderHover} ${themeConfig.borderActive}`}`}
          autoComplete="off"
          spellCheck="false"
          style={{
            fontSize: '16px' // Prevent auto-zoom on mobile
          }}
        />
        {isSearching && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="w-5 h-5 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}
      </div>

      {/* Search Results Dropdown */}
      {showResults && (
        <div
          className={`absolute z-50 w-full mt-2 rounded-xl shadow-2xl max-h-60 overflow-y-auto backdrop-blur-lg ${themeConfig.card} border ${themeConfig.border}`}
          ref={searchResultsRef}
        >
          {searchResults.length > 0 ? (
            searchResults.map((photo, index) => (
              <div
                key={`${photo.id}-${photo.photo_id}-${index}`}
                className={`flex items-center gap-3 p-3 cursor-pointer border-b transition-colors duration-200 last:border-0 ${themeConfig.interactiveHover} ${themeConfig.divider}`}
                onClick={() => onSelectPhoto(photo)}
                style={{
                  transition: 'all 0.2s ease'
                }}
              >
                {/* Photo Thumbnail */}
                <PhotoThumbnail
                  photo={photo}
                  className="w-10 h-10"
                />
                {/* Photo ID */}
                <div className="flex-1 truncate">
                  <p className={`${themeConfig.text} truncate`}>{photo.photo_id}</p>
                </div>
              </div>
            ))
          ) : (
            <div className={`p-3 ${themeConfig.textMuted} text-center`}>
              {t('no_photos_found')}
            </div>
          )}
        </div>
      )}
    </div>
  )
}