'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useLanguage } from '@/context/LanguageContext'

export default function TerminalAnimation() {
  const [currentStep, setCurrentStep] = useState(0)
  const { t } = useLanguage()



  const conversation = [
    {
      sender: 'customer',
      content: t('terminal_customer_1'),
      time: '03:30 AM'
    },
    {
      sender: 'bot',
      content: t('terminal_bot_1'),
      time: '03:30 AM'
    },
    {
      sender: 'customer',
      content: t('terminal_customer_2'),
      time: '03:30 AM'
    },
    {
      sender: 'bot',
      content: t('terminal_bot_2'),
      time: '03:31 AM'
    },
    {
      sender: 'customer',
      content: t('terminal_customer_3'),
      time: '03:31 AM'
    },
    {
      sender: 'bot',
      content: t('terminal_bot_3'),
      time: '03:32 AM'
    },
    {
      sender: 'customer',
      content: t('terminal_customer_4'),
      time: '03:32 AM'
    },
    {
      sender: 'bot',
      content: t('terminal_bot_4'),
      time: '03:33 AM'
    },
    {
      sender: 'customer',
      content: t('terminal_customer_5'),
      time: '03:33 AM'
    },
    {
      sender: 'bot',
      content: t('terminal_bot_5'),
      time: '03:33 AM'
    }
  ];

  useEffect(() => {
    if (currentStep < conversation.length - 1) {
      const timer = setTimeout(() => {
        setCurrentStep(prev => prev + 1)
      }, 1800)
      return () => clearTimeout(timer)
    } else {
      const timer = setTimeout(() => {
        setCurrentStep(0)
      }, 4000)
      return () => clearTimeout(timer)
    }
  }, [currentStep, conversation.length])

  return (
    <div
      className="terminal-container relative bg-gradient-to-br from-white/[0.08] to-white/[0.02] backdrop-blur-lg rounded-2xl border border-white/20 hover:border-white/50 transition-colors duration-300 overflow-hidden"
      style={{
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(255, 255, 255, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
      }}
    >

      {/* Chat Header */}
      <div className="terminal-header relative z-10 bg-gradient-to-r from-jade-purple/90 to-jade-purple/80 px-4 py-3 flex items-center" style={{
        boxShadow: '0 4px 16px rgba(134, 107, 255, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2)'
      }}>
        <div className="w-8 h-8 rounded-full bg-white flex items-center justify-center mr-2 text-jade-purple-dark font-bold shadow-md">
          <img src="/images/chhlat_500.png" alt="ChhlatBot AI Assistant Logo" className="w-8 h-8" />
        </div>
        <div className="flex-1">
          <div className="text-white font-title font-bold">ChhlatBot</div>
          <div className="text-white text-xs opacity-80 font-body flex items-center">
            <span className="w-1.5 h-1.5 bg-green-500 rounded-full mr-1 inline-block"></span>
            {t('terminal_active')}
          </div>
        </div>
        <div className="flex space-x-2">
          <div className="w-7 h-7 flex items-center justify-center text-white rounded-full hover:bg-white/20 transition-colors">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
            </svg>
          </div>
        </div>
      </div>

      {/* Chat Body */}
      <div className="relative z-10 h-[400px] sm:h-[450px] px-4 py-3 overflow-y-auto" style={{
        background: 'transparent'
      }}>
        <div className="text-center mb-4">
          <span className="terminal-date-badge text-xs font-medium bg-white/10 text-white px-3 py-2 rounded-full font-body border border-white/20" style={{
            boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)'
          }}>{t('terminal_today')}</span>
        </div>

        {conversation.slice(0, currentStep + 1).map((message, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className={`mb-4 flex ${message.sender === 'bot' ? 'justify-start' : 'justify-end'}`}
          >
            {message.sender === 'bot' && (
              <div className="w-7 h-7 rounded-full bg-white mr-1.5 flex-shrink-0 flex items-center justify-center" style={{
                boxShadow: '0 0 10px rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.3)'
              }}>
                <img src="/images/chhlat_500.png" alt="ChhlatBot AI Assistant responding to customer" className="w-6 h-6" />
              </div>
            )}

            <div className="max-w-[80%]">
              <div className={`rounded-2xl px-4 py-3 border ${
                message.sender === 'bot'
                  ? 'bg-jade-purple/30 text-white border-jade-purple/40'
                  : 'bg-white/10 text-white border-white/20'
              }`} style={{
                boxShadow: message.sender === 'bot'
                  ? '0 4px 16px rgba(134, 107, 255, 0.2)'
                  : '0 4px 16px rgba(0, 0, 0, 0.1)'
              }}>
                <p className="font-body text-sm leading-relaxed">{message.content}</p>
              </div>
              <div className="text-xs text-zinc-400 mt-1 font-body ml-2">{message.time}</div>
            </div>

            {message.sender === 'customer' && (
              <div className="w-7 h-7 rounded-full bg-jade-purple-dark ml-1.5 flex-shrink-0 flex items-center justify-center" style={{
                boxShadow: '0 0 10px rgba(255, 255, 255, 0.1), inset 0 0 5px rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.3)'
              }}>
                <span className="text-white text-xs font-medium">C</span>
              </div>
            )}
          </motion.div>
        ))}

        {/* Typing indicator for the next message */}
        {currentStep < conversation.length - 1 && (
          <div className={`flex ${conversation[currentStep + 1].sender === 'bot' ? 'justify-start' : 'justify-end'}`}>
            <motion.div
              className={`px-4 py-3 rounded-2xl border ${
                conversation[currentStep + 1].sender === 'bot'
                  ? 'bg-jade-purple/30 text-white mr-8 border-jade-purple/40'
                  : 'bg-white/10 text-white ml-8 border-white/20'
              }`}
              style={{
                boxShadow: conversation[currentStep + 1].sender === 'bot'
                  ? '0 4px 16px rgba(134, 107, 255, 0.2)'
                  : '0 4px 16px rgba(0, 0, 0, 0.1)'
              }}
              animate={{ opacity: [0.4, 0.7, 0.4] }}
              transition={{ repeat: Infinity, duration: 1.5 }}
            >
              <div className="flex space-x-1">
                <div className="w-2 h-2 rounded-full bg-current"></div>
                <div className="w-2 h-2 rounded-full bg-current"></div>
                <div className="w-2 h-2 rounded-full bg-current"></div>
              </div>
            </motion.div>
          </div>
        )}
      </div>

      {/* Chat Input Field */}
      <div className="terminal-footer relative z-10 bg-gradient-to-r from-jade-purple/90 to-jade-purple/80 px-4 py-3 border-t border-white/20 flex items-center" style={{
        boxShadow: '0 -4px 16px rgba(134, 107, 255, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2)'
      }}>
        <div className="w-9 h-9 rounded-xl flex items-center justify-center text-white/80 hover:text-white bg-jade-purple/5 hover:bg-white/10 transition-all duration-300 border border-white/30" style={{
          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)'
        }}>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
          </svg>
        </div>
        <div className="flex-1 bg-black/20 rounded-xl px-4 py-2.5 mx-3 text-white/80 font-body text-sm border border-white/20" style={{
          boxShadow: 'inset 0 2px 8px rgba(0, 0, 0, 0.2)'
        }}>
          {t('terminal_write_message')}
        </div>
        <div className="w-9 h-9 rounded-xl bg-jade-purple/5 hover:bg-white/10 flex items-center justify-center text-white/80 hover:text-white transition-all duration-300 border border-white/30 mr-2" style={{
          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)'
        }}>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18.75a6 6 0 006-6v-1.5m-6 7.5a6 6 0 01-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 01-3-3V4.5a3 3 0 116 0v8.25a3 3 0 01-3 3z"></path>
          </svg>
        </div>
        <div className="w-9 h-9 rounded-xl flex items-center justify-center text-white bg-jade-purple/5 hover:bg-white/10 transition-all duration-300 border border-white/30" style={{
          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)'
        }}>
          <svg className="w-4 h-4 transform rotate-90" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
          </svg>
        </div>
      </div>
    </div>
  )
}
