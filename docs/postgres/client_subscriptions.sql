CREATE TABLE public.client_subscriptions (
  id SERIAL NOT NULL,
  client_id text NOT NULL,
  plan_type TEXT NOT NULL,
  billing_cycle TEXT NOT NULL,
  start_date DATE NULL,
  next_billing_date DATE NULL,
  created_at TIMESTAMP WITH TIME ZONE NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NULL DEFAULT now(),
  
  CONSTRAINT client_subscriptions_pkey PRIMARY KEY (id),
  CONSTRAINT client_subscriptions_client_id_fkey FOREIGN KEY (client_id) REFERENCES clients (client_id)
);

-- Add trigger for auto-updating updated_at
CREATE TRIGGER trg_update_client_subscriptions_timestamp 
  BEFORE UPDATE ON public.client_subscriptions 
  FOR EACH ROW 
  EXECUTE FUNCTION public.update_modified_column();

-- Optional: Add index for common queries
CREATE INDEX IF NOT EXISTS idx_client_subscriptions_client_id ON public.client_subscriptions (client_id);