import jwt from 'jsonwebtoken'

/**
 * Generate a JWT token for webhook authentication
 */
export function generateWebhookToken(): string {
  const secret = process.env.CHHLAT_DB_WEBHOOK_TOKEN
  
  if (!secret) {
    throw new Error('CHHLAT_DB_WEBHOOK_TOKEN environment variable is required')
  }

  // Create JWT with 1 minute expiry
  const token = jwt.sign(
    {
      iat: Math.floor(Date.now() / 1000), // issued at
    },
    secret,
    {
      algorithm: 'HS256',
      expiresIn: '1m' // 1 minute expiry
    }
  )

  return token
}

/**
 * Verify a JWT token for webhook authentication
 */
export function verifyWebhookToken(token: string): boolean {
  const secret = process.env.CHHLAT_DB_WEBHOOK_TOKEN
  
  if (!secret) {
    throw new Error('CHHLAT_DB_WEBHOOK_TOKEN environment variable is required')
  }

  try {
    jwt.verify(token, secret, {
      algorithms: ['HS256']
    })

    return true
  } catch (error) {
    // Token is invalid, expired, or malformed
    return false
  }
}