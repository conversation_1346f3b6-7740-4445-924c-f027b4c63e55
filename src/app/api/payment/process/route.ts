import { NextResponse } from 'next/server';
import { verifyAuth } from '@/utils/auth';
import { nanoid } from 'nanoid';
import { pricingPlans } from '@/lib/pricing';

// Generate timestamp in required format: YYYYMMDDHHmmss
function generateTimestamp() {
  const now = new Date();
  const year = now.getUTCFullYear();
  const month = String(now.getUTCMonth() + 1).padStart(2, '0');
  const day = String(now.getUTCDate()).padStart(2, '0');
  const hours = String(now.getUTCHours()).padStart(2, '0');
  const minutes = String(now.getUTCMinutes()).padStart(2, '0');
  const seconds = String(now.getUTCSeconds()).padStart(2, '0');

  return `${year}${month}${day}${hours}${minutes}${seconds}`;
}

// Generate hash for PayWay API
function generateHash(
  req_time: string,
  merchant_id: string,
  tran_id: string,
  amount: string,
  items: string = '',
  shipping: string = '',
  firstname: string = '',
  lastname: string = '',
  email: string = '',
  phone: string = '',
  type: string = '',
  payment_option: string = '',
  return_url: string = '',
  cancel_url: string = '',
  continue_success_url: string = '',
  return_deeplink: string = '',
  currency: string = '',
  custom_fields: string = '',
  return_params: string = '',
  payout: string = '',
  lifetime: string = '',
  additional_params: string = '',
  google_pay_token: string = ''
) {
  const apiKey = process.env.PAYWAY_API_KEY || '';

  // Concatenate all parameters in the required order
  const dataToHash = req_time + merchant_id + tran_id + amount + items + shipping +
                    firstname + lastname + email + phone + type + payment_option +
                    return_url + cancel_url + continue_success_url + return_deeplink +
                    currency + custom_fields + return_params + payout + lifetime +
                    additional_params + google_pay_token;

  // Use crypto module for HMAC-SHA512
  const crypto = require('crypto');
  const hmac = crypto.createHmac('sha512', apiKey);
  hmac.update(dataToHash);

  // Return base64 encoded hash
  return Buffer.from(hmac.digest()).toString('base64');
}

export async function POST(request: Request) {
  try {
    // Verify authentication using optimized RLS-based auth
    const { authenticated, clientId } = await verifyAuth();
    if (!authenticated || !clientId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const { planType, billingCycle } = body;

    // Validate required parameters
    if (!planType || billingCycle === undefined) {
      return NextResponse.json(
        { error: 'Missing required parameters: planType, billingCycle' },
        { status: 400 }
      );
    }

    // Get plan price based on planType and billingCycle from the pricing configuration
    let amount = '0.00';
    let planName = '';
    const months = billingCycle === 0 ? 1 : 3;

    // Find the plan in pricingPlans
    const plan = pricingPlans.find(p => p.nameKey.replace('plan_', '').toLowerCase() === planType);

    if (!plan) {
      return NextResponse.json(
        { error: 'Invalid plan type' },
        { status: 400 }
      );
    }

    // Get the plan name
    planName = plan.nameKey.replace('plan_', '');
    planName = planName.charAt(0).toUpperCase() + planName.slice(1) + ' Plan';

    // Get the monthly price from the pricing configuration
    const monthlyPrice = plan.prices[billingCycle];

    // Calculate total amount based on billing cycle
    // For 1-month plans, use the monthly price
    // For 3-month plans, multiply the monthly price by 3
    amount = (monthlyPrice * (billingCycle === 0 ? 1 : 3)).toFixed(2);

    // Format transaction ID according to PayWay requirements
    // Must be alphanumeric, no special characters, max 20 characters
    const tran_id = `PW${Math.floor(Date.now() / 1000).toString().substring(0, 8)}${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;

    // Generate timestamp
    const req_time = generateTimestamp();

    // Use the exact merchant ID from environment variables
    const merchant_id = process.env.PAYWAY_MERCHANT_ID || '';


    // Base URL for return URLs
    // For local development with ngrok, you can override this with your ngrok URL
    // Example: const baseUrl = 'https://abc123.ngrok.io';
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || '';

    // Log the base URL

    // Prepare return URLs
    const return_url = `${baseUrl}/api/payment/callback`;
    const cancel_url = `${baseUrl}/dashboard/payment/cancel`;
    const continue_success_url = `${baseUrl}/dashboard/payment/success`;

    // Log the return URLs

    // Prepare items (base64 encoded JSON)
    const itemsData = [
      {
        name: `${planName} (${months} month${months > 1 ? 's' : ''})`,
        quantity: 1,
        price: parseFloat(amount)
      }
    ];
    const items = Buffer.from(JSON.stringify(itemsData)).toString('base64');

    // Prepare custom fields (base64 encoded JSON)
    const customFieldsData = {
      client_id: clientId,
      plan_type: planType,
      billing_cycle: billingCycle
    };
    const custom_fields = Buffer.from(JSON.stringify(customFieldsData)).toString('base64');

    // Set payment option to KHQR with deeplink
    const payment_option = 'abapay_khqr_deeplink';

    // Set transaction type
    const type = 'purchase';

    // Set currency
    const currency = 'USD';

    // Generate hash
    const hash = generateHash(
      req_time,
      merchant_id,
      tran_id,
      amount,
      items,
      '', // shipping
      '', // firstname
      '', // lastname
      '', // email
      '', // phone
      type, // type
      payment_option, // payment_option
      return_url,
      cancel_url,
      continue_success_url,
      '', // return_deeplink
      currency, // currency
      custom_fields
    );

    // Prepare the form data for PayWay API
    const formData = new FormData();
    formData.append('req_time', req_time);
    formData.append('merchant_id', merchant_id);
    formData.append('tran_id', tran_id);
    formData.append('amount', amount);
    formData.append('items', items);
    formData.append('type', type);
    formData.append('payment_option', payment_option);
    formData.append('return_url', return_url);
    formData.append('cancel_url', cancel_url);
    formData.append('continue_success_url', continue_success_url);
    formData.append('currency', currency);
    formData.append('custom_fields', custom_fields);
    formData.append('hash', hash);


    try {
      // Make direct API call to PayWay
      const payWayResponse = await fetch(process.env.PAYWAY_API_URL!, {
        method: 'POST',
        body: formData
      });

      // Check if the response is OK
      if (!payWayResponse.ok) {
        const errorText = await payWayResponse.text();
        console.error('PayWay API error:', errorText);
        throw new Error(`PayWay API error: ${payWayResponse.status} ${payWayResponse.statusText}`);
      }

      // Parse the response
      const payWayData = await payWayResponse.json();

      // Return the PayWay response to the client
      return NextResponse.json({
        success: true,
        paymentData: payWayData
      });
    } catch (error) {
      console.error('Error calling PayWay API:', error);
      throw error;
    }

  } catch (error: any) {
    console.error('Error in payment process API:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to process payment request' },
      { status: 500 }
    );
  }
}
