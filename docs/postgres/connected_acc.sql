CREATE TABLE public.connected_acc (
  id BIGSERIAL NOT NULL,
  platform TEXT NOT NULL,
  acc_id TEXT NOT NULL,
  client_id text NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  
  CONSTRAINT connected_acc_pkey PRIMARY KEY (id),
  CONSTRAINT connected_acc_platform_acc_id_key UNIQUE (platform, acc_id),
  CONSTRAINT connected_acc_client_id_fkey FOREIGN KEY (client_id) REFERENCES clients (client_id)
);

-- Only need index for auth_id since UNIQUE (platform, acc_id) already creates composite index
CREATE INDEX IF NOT EXISTS idx_connected_acc_client_id ON public.connected_acc (client_id);