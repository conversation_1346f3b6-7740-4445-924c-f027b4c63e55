import { NextResponse } from 'next/server'
import { serverCache } from '@/lib/cache'

export async function POST() {
  try {
    // Clear all server-side cache
    serverCache.clear()
    
    return NextResponse.json({
      success: true,
      message: 'Server cache cleared successfully'
    })
  } catch (error) {
    console.error('Error clearing server cache:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to clear server cache'
      },
      { status: 500 }
    )
  }
}