// Redis utility for audio code management
export class RedisClient {
  private baseUrl: string
  private token: string

  constructor() {
    this.baseUrl = process.env.UPSTASH_REDIS_REST_URL!
    this.token = process.env.UPSTASH_REDIS_REST_TOKEN!
    
    if (!this.baseUrl || !this.token) {
      throw new Error('Redis configuration missing: UPSTASH_REDIS_REST_URL and UPSTASH_REDIS_REST_TOKEN required')
    }
  }

  private async makeRequest(command: string[]) {
    const response = await fetch(`${this.baseUrl}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(command),
    })

    if (!response.ok) {
      throw new Error(`Redis request failed: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    return data.result
  }

  async get(key: string) {
    try {
      const result = await this.makeRequest(['GET', key])
      return result ? JSON.parse(result) : null
    } catch (error) {
      console.error('Redis GET error:', error)
      return null
    }
  }

  async set(key: string, value: any, ttlSeconds?: number) {
    try {
      const command = ['SET', key, JSON.stringify(value)]
      if (ttlSeconds) {
        command.push('EX', ttlSeconds.toString())
      }
      return await this.makeRequest(command)
    } catch (error) {
      console.error('Redis SET error:', error)
      return null
    }
  }

  async del(key: string) {
    try {
      return await this.makeRequest(['DEL', key])
    } catch (error) {
      console.error('Redis DEL error:', error)
      return null
    }
  }

  async exists(key: string) {
    try {
      return await this.makeRequest(['EXISTS', key])
    } catch (error) {
      console.error('Redis EXISTS error:', error)
      return false
    }
  }
}

// Audio-specific Redis operations
export class AudioRedisClient extends RedisClient {
  private getAudioKey(audioCode: string) {
    return `audio:${audioCode}`
  }

  async getAudioData(audioCode: string) {
    return await this.get(this.getAudioKey(audioCode))
  }

  async setAudioData(audioCode: string, audioData: any, ttlSeconds = 21600) { // 6 hours default
    return await this.set(this.getAudioKey(audioCode), audioData, ttlSeconds)
  }

  async markAudioProcessed(audioCode: string) {
    const audioData = await this.getAudioData(audioCode)
    if (audioData) {
      audioData.processed = true
      return await this.setAudioData(audioCode, audioData)
    }
    return null
  }

  async deleteAudioData(audioCode: string) {
    return await this.del(this.getAudioKey(audioCode))
  }

  async audioExists(audioCode: string) {
    return await this.exists(this.getAudioKey(audioCode))
  }
}

// Registration-specific Redis operations
export class RegistrationRedisClient extends RedisClient {
  private getRegistrationKey(code: string) {
    return `registration:${code}`
  }

  async setRegistrationData(code: string, data: { plan: string, username: string, lang: string, timeZone: string }, ttlSeconds = 900) {
    return await this.set(this.getRegistrationKey(code), data, ttlSeconds)
  }

  async getRegistrationData(code: string) {
    return await this.get(this.getRegistrationKey(code))
  }

  async deleteRegistrationData(code: string) {
    return await this.del(this.getRegistrationKey(code))
  }

  async registrationExists(code: string) {
    return await this.exists(this.getRegistrationKey(code))
  }
}

// Export singleton instances
export const audioRedis = new AudioRedisClient()
export const registrationRedis = new RegistrationRedisClient()