'use server'

import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export interface DashboardData {
  clientInfo: {
    client_id: string
    username: string
    sector: string | null
    lang: string | null
    plan_type: string | null
    next_billing_date: string | null
  }
  usageData: {
    usage_used: number
    usage_limit: number
  }
  knowledgeStats: {
    faqCount: number
    photoCount: number
    faqLimit: number
    photoLimit: number
    faqUsagePercentage: number
    photoUsagePercentage: number
  }
}

/**
 * Optimized dashboard data loading with single JOIN query
 * Combines client + config data in one database call
 */
export async function getDashboardData(): Promise<DashboardData | null> {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            // Not needed for this context
          },
          remove(name: string, options: any) {
            // Not needed for this context
          },
        },
      }
    )

    // Get client data with plan, billing info, and usage data (all from clients table)
    // RLS automatically filters by auth.uid()
    const { data, error } = await supabase
      .from('clients')
      .select('client_id, username, sector, lang, plan_type, next_billing_date, usage_used, usage_limit')
      .single()

    if (error || !data) {
      console.error('Error fetching client data:', error)
      return null
    }

    // Get FAQ count
    const { count: faqCount, error: faqError } = await supabase
      .from('faqs')
      .select('client_id', { count: 'exact', head: true })
      .eq('client_id', data.client_id)
      .eq('is_visible', true)

    if (faqError) {
      console.error('Error fetching FAQ count:', faqError)
    }

    // Get photo count
    const { count: photoCount, error: photoError } = await supabase
      .from('photos')
      .select('client_id', { count: 'exact', head: true })
      .eq('client_id', data.client_id)

    if (photoError) {
      console.error('Error fetching photo count:', photoError)
    }

    // Get plan limits from plans table
    let faqLimit = 0
    let photoLimit = 0

    if (data.plan_type) {
      const { data: planData, error: planError } = await supabase
        .from('plans')
        .select('total_faqs, total_photos')
        .ilike('name', data.plan_type.trim())
        .single()

      if (planError) {
        console.error('Error fetching plan limits:', planError)
      } else if (planData) {
        faqLimit = planData.total_faqs || 0
        photoLimit = planData.total_photos || 0
      }
    }

    // Calculate usage percentages
    const faqUsagePercentage = faqLimit > 0 ? Math.min((faqCount || 0) / faqLimit * 100, 100) : 0
    const photoUsagePercentage = photoLimit > 0 ? Math.min((photoCount || 0) / photoLimit * 100, 100) : 0

    const result = {
      clientInfo: {
        client_id: data.client_id,
        username: data.username,
        sector: data.sector,
        lang: data.lang,
        plan_type: data.plan_type,
        next_billing_date: data.next_billing_date
      },
      usageData: {
        usage_used: data.usage_used || 0,
        usage_limit: data.usage_limit || 2000
      },
      knowledgeStats: {
        faqCount: faqCount || 0,
        photoCount: photoCount || 0,
        faqLimit,
        photoLimit,
        faqUsagePercentage: Math.round(faqUsagePercentage),
        photoUsagePercentage: Math.round(photoUsagePercentage)
      }
    }

    return result
  } catch (error) {
    console.error('Error in getDashboardData:', error)
    return null
  }
}

// Server-side cache removed - using client-side caching only for better simplicity

/**
 * Get FAQ count and subscription limits in a single query
 * Optimized for knowledge page
 */
export async function getFaqCountAndLimits(): Promise<{
  faqCount: number
  subscriptionLimits: {
    plan_type: string | null
    usage_used: number
    usage_limit: number
  }
} | null> {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            // Not needed for this context
          },
          remove(name: string, options: any) {
            // Not needed for this context
          },
        },
      }
    )

    // Get client info with usage data (all from clients table)
    const { data: clientData, error: clientError } = await supabase
      .from('clients')
      .select('client_id, plan_type, usage_used, usage_limit')
      .single()

    if (clientError || !clientData) {
      console.error('Error fetching client data:', clientError)
      return null
    }

    // Get FAQ count - RLS automatically filters by client_id
    const { count, error: countError } = await supabase
      .from('faqs')
      .select('client_id', { count: 'exact', head: true })

    if (countError) {
      console.error('Error fetching FAQ count:', countError)
      return null
    }

    return {
      faqCount: count || 0,
      subscriptionLimits: {
        plan_type: clientData.plan_type,
        usage_used: clientData.usage_used || 0,
        usage_limit: clientData.usage_limit || 2000
      }
    }
  } catch (error) {
    console.error('Error in getFaqCountAndLimits:', error)
    return null
  }
}
