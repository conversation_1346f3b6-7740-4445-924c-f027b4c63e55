# Intro Management System

Manages intro (welcome chat) messages with text, audio, and photo content using explicit change detection patterns.

## System Overview

### Core Features
- **Text/Audio Intro Messages**: Create and edit with mode switching
- **Photo Attachment**: Search and attach photos from knowledge cache
- **Explicit Change Detection**: Eliminate race conditions with precise change tracking
- **Real-time Validation**: Instant feedback with debounced audio validation
- **Audio Processing**: Redis → Telegram → R2 pipeline for audio content

### Architecture
- **Data Loading**: Mixed strategy - knowledge cache for stats, fresh fetch for intro data
- **Single Record**: One intro per client in `welcome_chat` table
- **Cache Integration**: Uses dashboard cache for client metadata
- **API Endpoint**: `/api/knowledge/welcome-chat` for CRUD operations

## Explicit Change Detection

### Change Detection Strategy
**Philosophy**: Frontend calculates precise changes instead of backend guesswork
**Implementation**: Separate `useCallback` functions for each change type

### Change Types
1. **Text Answer Changes**: Compare final vs original text, handle empty states
2. **Audio Mode Changes**: Track text/audio mode switching
3. **Audio Code Changes**: Detect new recordings vs existing audio
4. **Photo Changes**: Track addition, removal, or replacement
5. **Photo-Only Changes**: Identify photo-only modifications (for webhook context)

### Benefits
- **Race Condition Elimination**: Precise change tracking prevents conflicts
- **Reduced API Calls**: Only send changed data to backend
- **Better UX**: Instant validation and feedback
- **Consistency**: Same pattern across all management systems

## Update Data Pattern

### Conditional Field Updates
**Strategy**: Only include changed fields in update payload
**Implementation**: Use change detection results to conditionally add fields

### Update Flow
1. **Base Data**: Always include photo_url, photo_id, is_audio, onlyPhoto flags
2. **Text Changes**: Add answer_p only if text answer changed
3. **Audio Changes**: Add audio context fields only if audio changed
4. **Photo Changes**: Clear platform ATM IDs only if photo changed

### Backend Processing
- **Context Fields**: Separated from database fields before SQL update
- **Webhook Body**: Include context fields for easier processing
- **Database Update**: Only actual database fields in SQL statement
- **Dynamic SQL**: Build update query based on provided fields

## Audio Processing

### Audio Pipeline
1. **Validation**: Redis-based audio code validation via `/api/audio/validate`
2. **Download**: Telegram audio download using file_id
3. **Conversion**: MP4A AAC format conversion with buffer validation
4. **Upload**: R2 storage via `/api/file/audios` with unique audio ID
5. **Cleanup**: Remove old audio files when switching to text mode

### Audio Features
- **Real-time Validation**: Debounced validation (500ms) with instant feedback
- **Audio Reusability**: Codes can be reused across different intro messages
- **Mode Switching**: Seamless transition between text and audio modes
- **File Management**: Automatic cleanup of unused audio files

### Processing Context
- **Audio ID Generation**: `${audioCode}-${nanoid(8)}` for uniqueness
- **Text Clearing**: Empty answer_p field for audio introductions
- **Storage Management**: Supabase cleanup for old files

## Photo Management

### Photo Integration
- **Shared System**: Uses same photo search as knowledge page
- **Cache-First**: Search local photos before API fallback
- **Attachment**: Single photo attachment per intro message
- **Gallery Support**: Store full URL array for modal display

### Photo Features
- **Search**: Local filtering with API fallback to `/api/knowledge/search-photos`
- **Selection**: Thumbnail display with full URL array storage
- **Clearing**: Reset selection and search state
- **ATM ID Management**: Clear platform-specific IDs when photo changes

## Data Loading Strategy

### Cache-First Pattern
- **Dashboard Cache**: Primary source for client_id, sector, lang
- **Selective Fallback**: Query database only for missing fields
- **Performance**: Reduces redundant database queries

### Data Sources
- **Knowledge Cache**: Statistics and photo data via `useKnowledgeData()`
- **Fresh Fetch**: Intro data fetched fresh each time (not cached)
- **Mixed Strategy**: Optimal balance between performance and freshness

## API Endpoints

### GET `/api/knowledge/welcome-chat` - Fetch Intro Data
**Purpose**: Retrieve single intro record per client
**Response**: Single record formatted as array for UI compatibility
**Processing**: Handles audio/photo existence detection

### PUT `/api/knowledge/welcome-chat` - Update Intro
**Purpose**: Update intro with explicit change detection
**Body**: `{ chat_id: string, updateData: object }`
**Features**:
- Conditional field updates based on change detection
- Audio processing pipeline integration
- Photo ATM ID management
- Webhook context fields for easier processing

### Common Patterns
- **Single Record**: One intro per client with chat_id format `${clientId}-1`
- **Cache-First**: Client data from dashboard cache with database fallback
- **Audio Integration**: Full audio processing pipeline support
- **Photo Support**: Array-based photo URL storage

## User Interface

### Edit Mode Features
- **Edit Toggle**: Switch between view and edit modes
- **Change Detection**: Show cancel confirmation only if changes exist
- **Real-time Validation**: Instant feedback for audio codes and text
- **Success Feedback**: Auto-hide success overlay after 1.5 seconds

### Audio Mode Interface
- **Mode Toggle**: Switch between text and audio input
- **Debounced Validation**: 500ms delay for audio code validation
- **Visual Feedback**: Real-time validation status display
- **Code Reusability**: Allow reuse of existing audio codes

### State Management
- **Edit State**: Track editing mode and changes
- **Audio State**: Validation status and mode switching
- **Photo State**: Selection and search results
- **Success State**: Update feedback with auto-reset

## Implementation Status

### Core Features ✅
- Explicit change detection system with race condition prevention
- Text/audio mode switching with seamless transitions
- Photo search and attachment from knowledge cache
- Real-time validation with debounced feedback
- Audio processing pipeline integration
- Cache-first data loading strategy

### Integration Points
- **Authentication**: User-scoped operations with `verifyAuth()`
- **Caching**: Dashboard cache for client data, fresh fetch for intro data
- **Storage**: R2 for audio files, knowledge cache for photos
- **Database**: Single record per client in welcome_chat table
- **Knowledge System**: Shared photo search and cache patterns

### Key Patterns
1. Use explicit change detection for precise updates
2. Separate context fields from database fields
3. Handle audio cleanup when switching modes
4. Provide instant UI feedback with local state
5. Cache client data before database operations
6. Use debounced validation for real-time feedback
7. Clear platform ATM IDs when photos change
8. Test with both text and audio modes