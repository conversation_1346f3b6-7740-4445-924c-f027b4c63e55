'use client'

import { useRef, useEffect } from 'react'
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'
import { Button } from '@/components/ui'

interface UpdateConfirmationModalProps {
  showConfirmation: boolean
  onCancel: () => void
  onConfirm: () => void
  count?: number
  title?: string
  message?: string
  isLoading?: boolean
}

export default function UpdateConfirmationModal({ 
  showConfirmation, 
  onCancel, 
  onConfirm,
  count,
  title,
  message,
  isLoading = false
}: UpdateConfirmationModalProps) {
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()
  const confirmModalRef = useRef<HTMLDivElement>(null)

  // Handle escape key to cancel
  useEffect(() => {
    if (showConfirmation) {
      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === 'Escape') {
          event.preventDefault()
          onCancel()
        }
      }
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [showConfirmation, onCancel])

  if (!showConfirmation) return null

  return (
    <div
      className={`fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50`}
    >
      <div
        ref={confirmModalRef}
        className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-jade-purple/[0.8] border-jade-purple/50'}`}
        onClick={(e: React.MouseEvent) => e.stopPropagation()}
      >
        {theme === 'dark' && (
          <div className="absolute inset-0 bg-gradient-to-br from-jade-purple/10 to-transparent opacity-50 rounded-2xl"></div>
        )}
        <div className="relative z-10">
          <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.text}`}>
            {title || `${t('update')} ${t('knowledge')} ${t('management')}`}
          </h3>
          <p className={`${themeConfig.textSecondary} mb-6 text-center`}>
            {message || (count ? t('confirm_update_questions').replace('{count}', count.toString()).replace('{plural}', count !== 1 ? 's' : '') : t('change_question_confirmation'))}
          </p>
          <div className="flex justify-between w-full space-x-4">
            <Button
              onClick={onCancel}
              variant="cancel"
              size="md"
              className="flex-1"
              disabled={isLoading}
            >
              {t('cancel')}
            </Button>
            <Button
              onClick={onConfirm}
              variant="primary"
              size="md"
              className="flex-1"
              isLoading={isLoading}
              disabled={isLoading}
            >
              {t('confirm')}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}