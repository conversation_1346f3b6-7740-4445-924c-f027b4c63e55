import { Metadata } from 'next'
import { getSEOTranslations } from './seo-translations'

export { type SEOTranslations } from './seo-translations'

// Re-export for backward compatibility
export const metadataTranslations = {
  km: getSEOTranslations('km'),
  en: getSEOTranslations('en')
}

export function generateMetadata(language: 'km' | 'en' = 'km'): Metadata {
  const translations = getSEOTranslations(language)
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://chhlat.com'
  
  return {
    metadataBase: new URL(baseUrl),
    title: translations.title,
    description: translations.description,
    
    // Open Graph tags for Facebook, Instagram, Telegram
    openGraph: {
      title: translations.openGraphTitle,
      description: translations.openGraphDescription,
      url: baseUrl,
      siteName: 'ChhlatBot',
      images: [
        {
          url: '/images/chhlat_500.png',
          width: 500,
          height: 500,
          alt: translations.logoAlt
        }
      ],
      locale: language === 'km' ? 'km_KH' : 'en_US',
      type: 'website',
    },
    
    // Additional meta tags
    alternates: {
      canonical: baseUrl,
      languages: {
        'km-KH': baseUrl,
        'en-US': baseUrl,
        'x-default': baseUrl
      }
    },
    
    // Keywords for better SEO
    keywords: translations.keywords,
    
    // Author and creator info
    authors: [{ name: 'ChhlatBot Team' }],
    creator: 'ChhlatBot',
    publisher: 'ChhlatBot',
    
    // Additional metadata
    category: 'Technology',
    classification: 'Business Software',
  }
}