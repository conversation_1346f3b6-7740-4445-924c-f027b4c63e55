# Knowledge Base Page System

Displays and manages existing FAQ entries with pagination, search, and CRUD operations.

## Architecture Overview

### Core Components
- **FAQ List Display**: Paginated table with search filtering
- **Recently Added Section**: Inline editing for latest FAQ
- **Photo Integration**: Search and attach photos from knowledge cache
- **Audio Support**: Record, upload, and playback audio answers

### Data Flow
1. **Initial Load**: Check component state → fetch via `/api/knowledge/lists` if empty
2. **Cache Strategy**: Uses knowledge cache for photos, fresh fetch for FAQ data
3. **Search**: Client-side filtering of loaded FAQ data
4. **Updates**: PUT endpoint with optimistic UI updates

### Key Patterns
- **Cache-First Client Lookup**: Dashboard cache → database fallback
- **N8N Response Handling**: Extract from array wrapper format
- **Explicit Change Detection**: Separate functions for answer/audio/photo changes
- **Optimistic Updates**: Update UI immediately, sync with backend

## Data Management

### FAQ Loading Strategy
- **Single Fetch**: Load all FAQs once on component mount
- **Client-Side Operations**: Search, filter, and paginate in memory
- **Cache Integration**: Photo data from knowledge cache, FAQ data fresh

### Search & Filtering
- **Real-Time Search**: Filter loaded data on question/answer text
- **Pagination**: Client-side slicing of filtered results
- **Performance**: No server requests for search/filter operations

## API Endpoints

### GET `/api/knowledge/lists` - Fetch FAQ List
**Purpose**: Load all visible FAQs for client
**Auth**: Required (verifyAuth)
**Response**: Array of FAQ objects with processed question/answer fields
**Caching**: Cache-first client lookup, fresh FAQ data

### PUT `/api/knowledge/lists` - Update FAQ
**Purpose**: Update FAQ with explicit change detection
**Auth**: Required (verifyAuth)
**Body**: `{ faq_id: string, updateData: object }`
**Features**: 
- Audio processing pipeline (Redis → Telegram → R2)
- Dynamic SQL query building
- Helper fields for webhook processing
- Optimistic UI updates (minimal response)

### DELETE `/api/knowledge/lists` - Delete FAQ
**Purpose**: Remove FAQ by faq_id
**Auth**: Required (verifyAuth)
**Query**: `?faq_id=string`
**Body**: `{ audio_file_path: string | null }`
**Response**: Deleted FAQ data or 404 if not found
**Features**: 
- Cache-first client lookup (dashboard cache → database fallback)
- Audio file cleanup from R2 storage before database deletion
- Knowledge cache updates (decrements FAQ count)
- Frontend sends audio_file_path for efficient cleanup
- Graceful cleanup failure handling

### Common Patterns
- **N8N Response Handling**: Extract from array wrapper format
- **Cache-First Client Lookup**: Dashboard cache → database fallback
- **Webhook Authentication**: JWT token generation and validation
- **Knowledge Cache Updates**: Server-side cache consistency for FAQ counts
- **Audio File Cleanup**: R2 storage cleanup before database deletion
- **Request Body Data**: Frontend provides cleanup data to avoid additional lookups

## Recently Added Section

### Explicit Change Detection Pattern
**Philosophy**: Only update fields that actually changed
**Implementation**: Separate `useCallback` functions for each change type
- `hasAnswerChanged()`: Compare text vs original, handle audio mode transitions
- `hasAudioChanged()`: Check audio mode changes and audio code changes
- `hasPhotoChanged()`: Compare photo selection vs original attachment
- `hasRealChanges()`: Aggregate function to validate any changes exist

### Update Flow
1. **Validation**: Check if real changes exist, validate audio codes
2. **Data Preparation**: Conditionally include only changed fields
3. **API Call**: PUT request with faq_id and updateData
4. **Optimistic Update**: Immediately update UI with frontend data
5. **Audio Processing**: Redis → Telegram → R2 pipeline for new audio

### Key Benefits
- **Efficiency**: Only sends changed data to backend
- **Consistency**: Same pattern as intro management system
- **User Experience**: Immediate UI feedback with optimistic updates
- **Validation**: Prevents unnecessary API calls for unchanged data

## Frontend Integration

### Photo Search Strategy
- **Primary**: Search cached photos from knowledge cache
- **Fallback**: API call to `/api/knowledge/photos` if cache unavailable
- **Performance**: Limit results to 5 matches, debounced search

### React Best Practices
- **Keys**: Use `qa.id` with fallback strategy for stable rendering
- **State Management**: Separate state for questions, filtered results, and UI state
- **Error Handling**: Comprehensive try-catch with user-friendly messages

### Response Processing
**N8N Pattern**: Always extract from array wrapper format
**Validation**: Check `success` field before processing `body`
**Fallbacks**: Handle empty responses and missing data gracefully

## Performance Strategy

### Caching Layers
1. **Server Cache**: Dashboard cache for client_id lookup
2. **Knowledge Cache**: Photo data via `useKnowledgeData()` hook
3. **Component State**: FAQ list with single fetch on mount

### Optimization Techniques
- **Single Fetch**: Load all FAQs once, avoid refetching
- **Client-Side Operations**: Search/filter/paginate in memory
- **Optimistic Updates**: Immediate UI response for better UX
- **Request Deduplication**: Prevent duplicate API calls

### Cache Update Strategy
- **Knowledge Cache**: Server-side updates for FAQ count consistency
- **Pattern**: Same as add-batch endpoint but with subtraction
- **Fallback**: Cache update failure doesn't break core operations
- **Cross-Session**: Updates visible across all user sessions immediately

## Implementation Status

### Core Features ✅
- FAQ list display with pagination and search
- Cache-first client lookup and N8N webhook integration
- Explicit change detection system for updates
- Audio processing pipeline (Redis → Telegram → R2)
- Photo search and attachment management
- Optimistic UI updates with error handling
- Knowledge cache updates for FAQ count consistency
- Audio file cleanup from R2 storage on deletion
- Efficient DELETE flow with minimal database queries

### Integration Points
- **Authentication**: All endpoints require `verifyAuth()`
- **Caching**: 3-tier strategy (server, hook, component)
- **Database**: N8N webhook with array wrapper handling
- **Storage**: R2 for audio files, knowledge cache for photos
- **Updates**: Conditional field updates with webhook helper fields

### Key Patterns
1. Always handle N8N array wrapper responses
2. Use cache-first strategies for client data
3. Implement explicit change detection before updates
4. Include webhook helper fields for easier processing
5. Optimize with single fetch + client-side operations
6. Validate data structures and provide fallback keys
7. Use optimistic updates for better user experience
8. Update server-side caches for cross-session consistency
9. Clean up storage resources before database deletion
10. Use frontend data to avoid unnecessary backend lookups