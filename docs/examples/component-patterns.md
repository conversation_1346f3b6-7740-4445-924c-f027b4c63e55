# Component Patterns

This document outlines the common React component patterns used throughout the chhlat-bot project.

## Component Structure

### Function Component Pattern
```typescript
// Standard function component with TypeScript
interface ComponentProps {
  title: string
  isVisible?: boolean
  onClose?: () => void
  children?: React.ReactNode
}

export default function ComponentName({ 
  title, 
  isVisible = true, 
  onClose, 
  children 
}: ComponentProps) {
  // Component logic here
  return (
    <div>
      {/* Component JSX */}
    </div>
  )
}
```

### Hook Integration Pattern
```typescript
// Component with custom hooks
import { useAuth } from '@/context/AuthContext'
import { useLanguage } from '@/context/LanguageContext'
import { useKnowledgeData } from '@/hooks/useOptimizedData'

export default function KnowledgeComponent() {
  const { user } = useAuth()
  const { t } = useLanguage()
  const { data: knowledgeData, loading, error } = useKnowledgeData()

  if (loading) return <LoadingSpinner />
  if (error) return <ErrorMessage message={error} />
  if (!user) return <SignInPrompt />

  return (
    <div>
      <h1>{t('knowledge.title')}</h1>
      {/* Component content */}
    </div>
  )
}
```

## Modal Component Pattern

### Basic Modal Structure
```typescript
// /src/components/ui/modals/BaseModal.tsx
interface BaseModalProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  children: React.ReactNode
}

export default function BaseModal({ 
  isOpen, 
  onClose, 
  title, 
  children 
}: BaseModalProps) {
  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50 transition-opacity"
        onClick={onClose}
      />
      
      {/* Modal content */}
      <div className="relative bg-white rounded-lg shadow-xl max-w-lg w-full mx-4">
        {/* Header */}
        {title && (
          <div className="px-6 py-4 border-b">
            <h2 className="text-lg font-semibold">{title}</h2>
            <button
              onClick={onClose}
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          </div>
        )}
        
        {/* Body */}
        <div className="px-6 py-4">
          {children}
        </div>
      </div>
    </div>
  )
}
```

### Confirmation Modal Pattern
```typescript
// /src/components/ui/modals/ConfirmationModal.tsx
interface ConfirmationModalProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  title: string
  message: string
  confirmText?: string
  cancelText?: string
  isLoading?: boolean
}

export default function ConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = "Confirm",
  cancelText = "Cancel",
  isLoading = false
}: ConfirmationModalProps) {
  return (
    <BaseModal isOpen={isOpen} onClose={onClose} title={title}>
      <div className="space-y-4">
        <p className="text-gray-600">{message}</p>
        
        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            disabled={isLoading}
            className="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
          >
            {cancelText}
          </button>
          
          <button
            onClick={onConfirm}
            disabled={isLoading}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
          >
            {isLoading ? 'Processing...' : confirmText}
          </button>
        </div>
      </div>
    </BaseModal>
  )
}
```

## Form Component Pattern

### Input Component
```typescript
// /src/components/ui/forms/Input.tsx
interface InputProps {
  label?: string
  value: string
  onChange: (value: string) => void
  placeholder?: string
  type?: 'text' | 'email' | 'password' | 'number'
  required?: boolean
  error?: string
  disabled?: boolean
}

export default function Input({
  label,
  value,
  onChange,
  placeholder,
  type = 'text',
  required = false,
  error,
  disabled = false
}: InputProps) {
  return (
    <div className="space-y-2">
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500">*</span>}
        </label>
      )}
      
      <input
        type={type}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        disabled={disabled}
        className={`
          w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
          ${error ? 'border-red-500' : 'border-gray-300'}
          ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}
        `}
      />
      
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
    </div>
  )
}
```

### Form Container Pattern
```typescript
// /src/components/ui/forms/FormContainer.tsx
interface FormContainerProps {
  onSubmit: (e: React.FormEvent) => void
  children: React.ReactNode
  className?: string
}

export default function FormContainer({ 
  onSubmit, 
  children, 
  className = '' 
}: FormContainerProps) {
  return (
    <form 
      onSubmit={onSubmit}
      className={`space-y-4 ${className}`}
    >
      {children}
    </form>
  )
}
```

## Loading & Error States

### Loading Component
```typescript
// /src/components/ui/LoadingSpinner.tsx
interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg'
  text?: string
}

export default function LoadingSpinner({ 
  size = 'md', 
  text 
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  }

  return (
    <div className="flex items-center justify-center space-x-2">
      <div 
        className={`${sizeClasses[size]} animate-spin rounded-full border-2 border-gray-300 border-t-blue-600`}
      />
      {text && (
        <span className="text-gray-600">{text}</span>
      )}
    </div>
  )
}
```

### Error Boundary Pattern
```typescript
// /src/components/ui/ErrorBoundary.tsx
interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
}

export class ErrorBoundary extends React.Component<
  React.PropsWithChildren<{}>,
  ErrorBoundaryState
> {
  constructor(props: React.PropsWithChildren<{}>) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error Boundary caught an error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex items-center justify-center min-h-[200px]">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-red-600 mb-2">
              Something went wrong
            </h2>
            <p className="text-gray-600 mb-4">
              {this.state.error?.message || 'An unexpected error occurred'}
            </p>
            <button
              onClick={() => this.setState({ hasError: false })}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Try again
            </button>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}
```

## Data Display Patterns

### List Component Pattern
```typescript
// /src/components/ui/List.tsx
interface ListItem {
  id: string
  [key: string]: any
}

interface ListProps<T extends ListItem> {
  items: T[]
  renderItem: (item: T, index: number) => React.ReactNode
  loading?: boolean
  error?: string
  emptyMessage?: string
  className?: string
}

export default function List<T extends ListItem>({
  items,
  renderItem,
  loading = false,
  error,
  emptyMessage = 'No items found',
  className = ''
}: ListProps<T>) {
  if (loading) {
    return <LoadingSpinner text="Loading items..." />
  }

  if (error) {
    return (
      <div className="text-center text-red-600 py-4">
        Error: {error}
      </div>
    )
  }

  if (items.length === 0) {
    return (
      <div className="text-center text-gray-500 py-4">
        {emptyMessage}
      </div>
    )
  }

  return (
    <div className={`space-y-2 ${className}`}>
      {items.map((item, index) => (
        <div key={item.id}>
          {renderItem(item, index)}
        </div>
      ))}
    </div>
  )
}
```

### Card Component Pattern
```typescript
// /src/components/ui/Card.tsx
interface CardProps {
  title?: string
  children: React.ReactNode
  actions?: React.ReactNode
  className?: string
}

export default function Card({ 
  title, 
  children, 
  actions, 
  className = '' 
}: CardProps) {
  return (
    <div className={`bg-white rounded-lg shadow-md border ${className}`}>
      {/* Header */}
      {(title || actions) && (
        <div className="px-6 py-4 border-b flex justify-between items-center">
          {title && (
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          )}
          {actions && (
            <div className="flex space-x-2">
              {actions}
            </div>
          )}
        </div>
      )}
      
      {/* Content */}
      <div className="px-6 py-4">
        {children}
      </div>
    </div>
  )
}
```

## Button Component Pattern

### Button Variants
```typescript
// /src/components/ui/Button.tsx
type ButtonVariant = 'primary' | 'secondary' | 'danger' | 'outline'
type ButtonSize = 'sm' | 'md' | 'lg'

interface ButtonProps {
  children: React.ReactNode
  onClick?: () => void
  variant?: ButtonVariant
  size?: ButtonSize
  disabled?: boolean
  loading?: boolean
  type?: 'button' | 'submit' | 'reset'
  className?: string
}

export default function Button({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  type = 'button',
  className = ''
}: ButtonProps) {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2'
  
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500',
    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',
    outline: 'border border-gray-300 bg-transparent text-gray-700 hover:bg-gray-50 focus:ring-blue-500'
  }
  
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  }

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled || loading}
      className={`
        ${baseClasses}
        ${variantClasses[variant]}
        ${sizeClasses[size]}
        ${disabled || loading ? 'opacity-50 cursor-not-allowed' : ''}
        ${className}
      `}
    >
      {loading && (
        <LoadingSpinner size="sm" />
      )}
      {children}
    </button>
  )
}
```

## Image Component Pattern

### Optimized Image Component
```typescript
// /src/components/ui/OptimizedImage.tsx
import { useState } from 'react'
import { optimizeImage } from '@/utils/imageOptimization'

interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  lazy?: boolean
  placeholder?: string
}

export default function OptimizedImage({
  src,
  alt,
  width = 800,
  height = 600,
  className = '',
  lazy = true,
  placeholder = '/placeholder-image.svg'
}: OptimizedImageProps) {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)

  const optimizedSrc = optimizeImage(src, { width, height })

  return (
    <div className={`relative ${className}`}>
      {/* Loading placeholder */}
      {!imageLoaded && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse rounded" />
      )}
      
      {/* Actual image */}
      <img
        src={imageError ? placeholder : optimizedSrc}
        alt={alt}
        loading={lazy ? 'lazy' : 'eager'}
        className={`
          w-full h-full object-cover rounded transition-opacity
          ${imageLoaded ? 'opacity-100' : 'opacity-0'}
        `}
        onLoad={() => setImageLoaded(true)}
        onError={() => {
          setImageError(true)
          setImageLoaded(true)
        }}
      />
    </div>
  )
}
```

## Search Component Pattern

### Search Input with Debounce
```typescript
// /src/components/ui/SearchInput.tsx
import { useState, useEffect, useCallback } from 'react'

interface SearchInputProps {
  onSearch: (query: string) => void
  placeholder?: string
  debounceMs?: number
  className?: string
}

export default function SearchInput({
  onSearch,
  placeholder = 'Search...',
  debounceMs = 300,
  className = ''
}: SearchInputProps) {
  const [query, setQuery] = useState('')

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((searchQuery: string) => {
      onSearch(searchQuery)
    }, debounceMs),
    [onSearch, debounceMs]
  )

  useEffect(() => {
    debouncedSearch(query)
  }, [query, debouncedSearch])

  return (
    <div className={`relative ${className}`}>
      <input
        type="text"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder={placeholder}
        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
      />
      
      {/* Search icon */}
      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </div>
      
      {/* Clear button */}
      {query && (
        <button
          onClick={() => setQuery('')}
          className="absolute inset-y-0 right-0 pr-3 flex items-center"
        >
          <svg className="w-5 h-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      )}
    </div>
  )
}

// Debounce utility function
function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }
}
```

## Performance Patterns

### Memoized Component
```typescript
// /src/components/ui/MemoizedComponent.tsx
import { memo, useMemo } from 'react'

interface MemoizedComponentProps {
  data: any[]
  onItemClick: (item: any) => void
  filter?: string
}

const MemoizedComponent = memo(function MemoizedComponent({
  data,
  onItemClick,
  filter = ''
}: MemoizedComponentProps) {
  // Memoize expensive calculations
  const filteredData = useMemo(() => {
    if (!filter) return data
    return data.filter(item => 
      item.name.toLowerCase().includes(filter.toLowerCase())
    )
  }, [data, filter])

  const sortedData = useMemo(() => {
    return [...filteredData].sort((a, b) => a.name.localeCompare(b.name))
  }, [filteredData])

  return (
    <div>
      {sortedData.map(item => (
        <div
          key={item.id}
          onClick={() => onItemClick(item)}
          className="cursor-pointer hover:bg-gray-100 p-2"
        >
          {item.name}
        </div>
      ))}
    </div>
  )
})

export default MemoizedComponent
```

### Virtualized List Pattern
```typescript
// /src/components/ui/VirtualizedList.tsx
import { useMemo } from 'react'
import { FixedSizeList as List } from 'react-window'

interface VirtualizedListProps {
  items: any[]
  height: number
  itemHeight: number
  renderItem: (item: any, index: number) => React.ReactNode
}

export default function VirtualizedList({
  items,
  height,
  itemHeight,
  renderItem
}: VirtualizedListProps) {
  const Row = useMemo(() => {
    return ({ index, style }: { index: number; style: React.CSSProperties }) => (
      <div style={style}>
        {renderItem(items[index], index)}
      </div>
    )
  }, [items, renderItem])

  return (
    <List
      height={height}
      itemCount={items.length}
      itemSize={itemHeight}
      itemData={items}
    >
      {Row}
    </List>
  )
}
```

## Best Practices

1. **Use TypeScript** for all components with proper prop typing
2. **Implement loading and error states** for better UX
3. **Use memoization** for expensive calculations
4. **Follow consistent naming conventions**
5. **Create reusable components** for common UI patterns
6. **Use proper accessibility attributes**
7. **Handle edge cases** (empty states, errors)
8. **Optimize images** with the imageOptimization utility
9. **Use debouncing** for search inputs
10. **Implement proper error boundaries**

## Component Organization

```
src/components/
├── ui/                    # Reusable UI components
│   ├── Button.tsx
│   ├── Input.tsx
│   ├── Modal.tsx
│   └── forms/            # Form-specific components
│       ├── FormContainer.tsx
│       ├── ImageUploadArea.tsx
│       └── PhotoIdInput.tsx
├── features/             # Feature-specific components
│   ├── dashboard/
│   ├── knowledge/
│   └── photos/
└── layout/              # Layout components
    ├── Header.tsx
    ├── Footer.tsx
    └── Sidebar.tsx
```

This structure promotes reusability, maintainability, and consistent design patterns across the application.