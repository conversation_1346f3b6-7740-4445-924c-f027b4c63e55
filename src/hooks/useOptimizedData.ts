'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
// Removed cache imports - using simple sessionStorage for dashboard cache only

export interface UseOptimizedDataOptions {
  enableCache?: boolean
  refetchInterval?: number
  staleTime?: number
}

export interface PhotoData {
  id: number
  photo_id: string
  photo_url: string[] | null
  photo_file_path: string[] | null
  updated_at?: string
}

export interface FaqData {
  id: string
  question: string
  answer: string
  audio_url?: string
  photo_url?: string
  updated_at: string
}

export interface DashboardData {
  clientInfo: {
    username: string
    sector: string | null
    lang: string | null
    plan_type: string | null
    next_billing_date: string | null
  }
  usageData: {
    usage_used: number
    usage_limit: number
  }
}

// Global request deduplication for API calls
let dashboardRequestPromise: Promise<any> | null = null
let knowledgeRequestPromise: Promise<any> | null = null
let photosRequestPromise: Promise<any> | null = null

// Simple encryption/decryption for session storage
const ENCRYPTION_KEY = 'chhlat_dashboard_2025' 

const simpleEncrypt = (text: string): string => {
  try {
    // Simple XOR encryption with base64 encoding for obfuscation
    const key = ENCRYPTION_KEY
    let encrypted = ''

    for (let i = 0; i < text.length; i++) {
      const keyChar = key.charCodeAt(i % key.length)
      const textChar = text.charCodeAt(i)
      encrypted += String.fromCharCode(textChar ^ keyChar)
    }

    // Base64 encode the result to make it unreadable
    return btoa(encrypted)
  } catch (error) {
    console.warn('Encryption failed, storing as plain text:', error)
    return text
  }
}

const simpleDecrypt = (encryptedText: string): string => {
  try {
    // Decode base64 first
    const decoded = atob(encryptedText)
    const key = ENCRYPTION_KEY
    let decrypted = ''

    for (let i = 0; i < decoded.length; i++) {
      const keyChar = key.charCodeAt(i % key.length)
      const encryptedChar = decoded.charCodeAt(i)
      decrypted += String.fromCharCode(encryptedChar ^ keyChar)
    }

    return decrypted
  } catch (error) {
    console.warn('Decryption failed:', error)
    return encryptedText
  }
}

// Dashboard cache functions removed - now using server-side caching

/**
 * Trigger dashboard refresh after FAQ/photo changes
 * Call this after adding/deleting FAQs or photos to refresh dashboard data
 */
export const triggerDashboardRefresh = (): void => {
  if (typeof window === 'undefined') return
  
  // Trigger dashboard refresh event
  window.dispatchEvent(new CustomEvent('dashboardRefresh'))
}

/**
 * Hook for dashboard data fetching with server-side caching
 */
export function useDashboardData(options: UseOptimizedDataOptions = {}) {
  const [data, setData] = useState<DashboardData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // If there's already a request in progress, wait for it instead of making a new one
      if (dashboardRequestPromise) {
        const result = await dashboardRequestPromise
        setData(result)
        return
      }

      // Use unified API route with dashboard cache
      dashboardRequestPromise = fetch('/api/user/data?cache=dashboard', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      }).then(async (response) => {
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }
        
        const responseData = await response.json()
        if (!responseData.success) {
          throw new Error(responseData.error_msg || 'Failed to fetch dashboard data')
        }
        
        // Return dashboard data (no knowledge stats)
        return responseData.body
      })

      const result = await dashboardRequestPromise
      setData(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
      // Clear the request promise after completion (success or failure)
      dashboardRequestPromise = null
    }
  }, [])

  // Fetch data on mount
  useEffect(() => {
    fetchData()
  }, []) // Empty dependency array - run only once on mount

  // Listen for manual refresh events
  useEffect(() => {
    const handleRefresh = () => {
      fetchData()
    }

    // Add event listener for manual refresh
    window.addEventListener('dashboardRefresh', handleRefresh)

    // Cleanup
    return () => {
      window.removeEventListener('dashboardRefresh', handleRefresh)
    }
  }, []) // Empty dependency array - stable event listener

  return { 
    data, 
    loading, 
    error, 
    refetch: fetchData
  }
}

/**
 * Hook for optimized knowledge cache data (knowledgeStats + photos + clientLang)
 */
export function useKnowledgeData(options: UseOptimizedDataOptions = {}) {
  const [data, setData] = useState<{
    sector: string | null
    clientLang: string
    knowledgeStats: {
      faqCount: number
      photoCount: number
      faqLimit: number
      photoLimit: number
      faqUsagePercentage: number
      photoUsagePercentage: number
    }
    photos: PhotoData[]
  } | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // If there's already a request in progress, wait for it instead of making a new one
      if (knowledgeRequestPromise) {
        const result = await knowledgeRequestPromise
        setData(result)
        return
      }

      // Use unified API route with knowledge cache
      knowledgeRequestPromise = fetch('/api/user/data?cache=knowledge', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      }).then(async (response) => {
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }
        
        const responseData = await response.json()
        if (!responseData.success) {
          throw new Error(responseData.error_msg || 'Failed to fetch knowledge data')
        }
        
        const knowledgeData = responseData.body
        
        return {
          sector: knowledgeData.sector,
          clientLang: knowledgeData.clientLang,
          knowledgeStats: knowledgeData.knowledgeStats,
          photos: knowledgeData.photos || []
        }
      })

      const result = await knowledgeRequestPromise
      setData(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
      // Clear the request promise after completion (success or failure)
      knowledgeRequestPromise = null
    }
  }, [])

  // Local state mutation functions for instant UI updates
  const addPhotoToState = useCallback((newPhoto: PhotoData) => {
    setData(prevData => {
      if (!prevData) return prevData
      
      const updatedPhotos = [newPhoto, ...prevData.photos]
      const newPhotoCount = updatedPhotos.length
      const photoLimit = prevData.knowledgeStats.photoLimit || 1
      
      return {
        ...prevData,
        photos: updatedPhotos,
        knowledgeStats: {
          ...prevData.knowledgeStats,
          photoCount: newPhotoCount,
          photoUsagePercentage: Math.round((newPhotoCount / photoLimit) * 100)
        }
      }
    })
  }, [])
  
  const updatePhotoInState = useCallback((updatedPhoto: PhotoData) => {
    setData(prevData => {
      if (!prevData) return prevData
      
      const updatedPhotos = prevData.photos.map(photo => 
        photo.photo_id === updatedPhoto.photo_id ? updatedPhoto : photo
      )
      
      return {
        ...prevData,
        photos: updatedPhotos
      }
    })
  }, [])
  
  const removePhotoFromState = useCallback((photoId: string) => {
    setData(prevData => {
      if (!prevData) return prevData
      
      const filteredPhotos = prevData.photos.filter(photo => photo.photo_id !== photoId)
      const newPhotoCount = filteredPhotos.length
      const photoLimit = prevData.knowledgeStats.photoLimit || 1
      
      return {
        ...prevData,
        photos: filteredPhotos,
        knowledgeStats: {
          ...prevData.knowledgeStats,
          photoCount: newPhotoCount,
          photoUsagePercentage: Math.round((newPhotoCount / photoLimit) * 100)
        }
      }
    })
  }, [])

  const updateFaqCountInState = useCallback((newFaqCount: number) => {
    setData(prevData => {
      if (!prevData) return prevData
      
      const faqLimit = Number(prevData.knowledgeStats.faqLimit) || 1
      const numericFaqCount = Number(newFaqCount) || 0
      
      return {
        ...prevData,
        knowledgeStats: {
          ...prevData.knowledgeStats,
          faqCount: numericFaqCount,
          faqUsagePercentage: Math.round((numericFaqCount / faqLimit) * 100)
        }
      }
    })
  }, [])

  useEffect(() => {
    fetchData()
  }, []) // Empty dependency array - run only once on mount

  return { 
    data, 
    loading, 
    error, 
    refetch: fetchData,
    // Local state mutation functions
    addPhotoToState,
    updatePhotoInState,
    removePhotoFromState,
    updateFaqCountInState
  }
}



// Removed useFaqData - replaced by direct Supabase calls in knowledge pages

// Removed usePhotoData - replaced by direct Supabase calls in knowledge pages

// Removed useFaqMutations - replaced by direct Supabase calls in knowledge pages

// Removed usePhotoMutations - replaced by direct Supabase calls in knowledge pages


/**
 * Hook for optimized photo data with server-side caching
 */
export function usePhotosData(options: UseOptimizedDataOptions = {}) {
  const [data, setData] = useState<PhotoData[] | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  const fetchPhotos = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // If there's already a request in progress, wait for it instead of making a new one
      if (photosRequestPromise) {
        const result = await photosRequestPromise
        setData(result)
        return
      }

      // Use unified API route with knowledge cache
      photosRequestPromise = fetch('/api/user/data?cache=knowledge', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      }).then(async (response) => {
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }
        
        const responseData = await response.json()
        if (!responseData.success) {
          throw new Error(responseData.error_msg || 'Failed to fetch photos')
        }
        
        // Extract photos array from knowledge cache response
        const knowledgeData = responseData.body
        const photosArray = Array.isArray(knowledgeData.photos) ? knowledgeData.photos : []
        return photosArray.map((row: any) => ({
          id: row.id,
          photo_id: row.photo_id,
          photo_url: row.photo_url,
          photo_file_path: row.photo_file_path,
          updated_at: row.updated_at
        }))
      })

      const result = await photosRequestPromise
      setData(result)
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error'))
    } finally {
      setLoading(false)
      // Clear the request promise after completion (success or failure)
      photosRequestPromise = null
    }
  }, [])

  // Fetch data on mount
  useEffect(() => {
    fetchPhotos()
  }, []) // Empty dependency array - run only once on mount

  return {
    data,
    loading,
    error,
    refetch: fetchPhotos,
    setData
  }
}

export const clearAllCaches = (): void => {
  if (typeof window === 'undefined') return

  try {
    // All caches are now server-side, no client-side cache to clear
  } catch (error) {
    console.warn('Error clearing caches:', error)
  }
}

/**
 * Legacy cache functions - now no-ops since we use server-side caching
 * Keeping for backward compatibility during migration
 */
export const updateFaqCountInCache = (newCount: number): void => {
  // No-op: FAQ count is now managed server-side
}

export const updatePhotoCountInCache = (newCount: number): void => {
  // No-op: Photo count is now managed server-side  
}

export const updatePhotosInCache = (photos: PhotoData[]): void => {
  // No-op: Photos are now cached server-side
}

export const getPhotosFromCache = (): PhotoData[] | null => {
  // No-op: Photos are now fetched via API with server-side cache
  return null
}
