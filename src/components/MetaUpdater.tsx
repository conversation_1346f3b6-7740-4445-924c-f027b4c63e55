'use client'

import { useEffect } from 'react'
import { useLanguage } from '@/context/LanguageContext'
import { getSEOTranslations } from '@/lib/seo-translations'

export default function MetaUpdater() {
  const { language } = useLanguage()
  
  useEffect(() => {
    // Ensure we're on the client side
    if (typeof window === 'undefined') return
    
    const translations = getSEOTranslations(language)
    
    // Debounce updates to prevent excessive DOM manipulation
    const timeoutId = setTimeout(() => {
      try {
        // Update document title
        if (document.title !== translations.title) {
          document.title = translations.title
        }
        
        // Update meta description
        const metaDescription = document.querySelector('meta[name="description"]')
        if (metaDescription) {
          metaDescription.setAttribute('content', translations.description)
        }
        
        // Update Open Graph tags
        const ogTitle = document.querySelector('meta[property="og:title"]')
        if (ogTitle) {
          ogTitle.setAttribute('content', translations.openGraphTitle)
        }
        
        const ogDescription = document.querySelector('meta[property="og:description"]')
        if (ogDescription) {
          ogDescription.setAttribute('content', translations.openGraphDescription)
        }
        
        const ogLocale = document.querySelector('meta[property="og:locale"]')
        if (ogLocale) {
          ogLocale.setAttribute('content', language === 'km' ? 'km_KH' : 'en_US')
        }
        
        // Update image alt text
        const ogImageAlt = document.querySelector('meta[property="og:image:alt"]')
        if (ogImageAlt) {
          ogImageAlt.setAttribute('content', translations.logoAlt)
        }
        
        // Update keywords meta tag if it exists
        const keywordsMeta = document.querySelector('meta[name="keywords"]')
        if (keywordsMeta) {
          const keywords = language === 'km' ? [
            'ChhlatBot',
            'ស្វ័យប្រវត្តិការតបសារ',
            'បណ្តាញសង្គម',
            'AI',
            'Facebook',
            'Instagram',
            'Telegram',
            'អាជីវកម្មកម្ពុជា',
            'ភាសាខ្មែរ',
            'សេវាកម្មអតិថិជន'
          ] : [
            'ChhlatBot',
            'social media automation',
            'AI chatbot',
            'Cambodia',
            'Khmer language',
            'Facebook automation',
            'Instagram automation',
            'Telegram automation',
            'customer service',
            'business automation'
          ]
          keywordsMeta.setAttribute('content', keywords.join(', '))
        }
        
        // Update HTML lang attribute
        if (document.documentElement.lang !== language) {
          document.documentElement.lang = language
        }
        
        // Update any other language-specific attributes
        const htmlElement = document.documentElement
        htmlElement.setAttribute('dir', language === 'km' ? 'ltr' : 'ltr') // Both languages use LTR
        
      } catch (error) {
        console.warn('MetaUpdater: Error updating meta tags:', error)
      }
    }, 100) // 100ms debounce
    
    return () => clearTimeout(timeoutId)
  }, [language])
  
  return null // This component doesn't render anything
}